<?php
/**
 * Authentication Class for OmniStore
 *
 * Handles user authentication, registration, login, logout,
 * password reset, and email verification.
 */

class Auth {
    private $db;
    private $maxLoginAttempts = MAX_LOGIN_ATTEMPTS;
    private $lockoutTime = LOGIN_LOCKOUT_TIME;

    public function __construct() {
        $this->db = getDB();
    }

    /**
     * Register a new user
     */
    public function register($data) {
        try {
            // Validate input data
            $validation = $this->validateRegistrationData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }

            // Check if email already exists
            if ($this->emailExists($data['email'])) {
                return ['success' => false, 'message' => 'Email address already registered'];
            }

            // Hash password
            $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);

            // Generate email verification token
            $verificationToken = generateRandomString(64);

            // Insert user into database
            $sql = "INSERT INTO users (first_name, last_name, email, password_hash, email_verification_token, theme_preference, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";

            $userId = $this->db->insert($sql, [
                $data['first_name'],
                $data['last_name'],
                $data['email'],
                $passwordHash,
                $verificationToken,
                $data['theme_preference'] ?? 'auto'
            ]);

            if ($userId) {
                // Send verification email
                $this->sendVerificationEmail($data['email'], $verificationToken);

                return [
                    'success' => true,
                    'message' => 'Registration successful! Please check your email to verify your account.',
                    'user_id' => $userId
                ];
            } else {
                return ['success' => false, 'message' => 'Registration failed. Please try again.'];
            }

        } catch (Exception $e) {
            logError("Registration error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Registration failed. Please try again.'];
        }
    }

    /**
     * Login user
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            // Check for too many login attempts
            if ($this->isAccountLocked($email)) {
                return ['success' => false, 'message' => 'Account temporarily locked due to too many failed attempts. Please try again later.'];
            }

            // Get user by email
            $user = $this->getUserByEmail($email);
            if (!$user) {
                $this->recordFailedAttempt($email);
                return ['success' => false, 'message' => 'Invalid email or password'];
            }

            // Check if account is active
            if ($user['status'] !== 'active') {
                return ['success' => false, 'message' => 'Account is not active. Please contact support.'];
            }

            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedAttempt($email);
                return ['success' => false, 'message' => 'Invalid email or password'];
            }

            // Check if email is verified
            if (!$user['email_verified']) {
                return ['success' => false, 'message' => 'Please verify your email address before logging in.'];
            }

            // Clear failed attempts
            $this->clearFailedAttempts($email);

            // Update last login
            $this->updateLastLogin($user['id']);

            // Create session
            $this->createUserSession($user, $rememberMe);

            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name'],
                    'email' => $user['email'],
                    'theme_preference' => $user['theme_preference']
                ]
            ];

        } catch (Exception $e) {
            logError("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed. Please try again.'];
        }
    }

    /**
     * Logout user
     */
    public function logout() {
        // Clear session data
        $_SESSION = [];

        // Destroy session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }

        // Clear remember me cookie if exists
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }

        // Destroy session
        session_destroy();

        return ['success' => true, 'message' => 'Logged out successfully'];
    }

    /**
     * Verify email address
     */
    public function verifyEmail($token) {
        try {
            $sql = "SELECT id FROM users WHERE email_verification_token = ? AND email_verified = 0";
            $user = $this->db->fetchOne($sql, [$token]);

            if (!$user) {
                return ['success' => false, 'message' => 'Invalid or expired verification token'];
            }

            // Update user as verified
            $sql = "UPDATE users SET email_verified = 1, email_verification_token = NULL WHERE id = ?";
            $this->db->update($sql, [$user['id']]);

            return ['success' => true, 'message' => 'Email verified successfully! You can now log in.'];

        } catch (Exception $e) {
            logError("Email verification error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Email verification failed. Please try again.'];
        }
    }

    /**
     * Request password reset
     */
    public function requestPasswordReset($email) {
        try {
            $user = $this->getUserByEmail($email);
            if (!$user) {
                // Don't reveal if email exists or not
                return ['success' => true, 'message' => 'If the email exists, a password reset link has been sent.'];
            }

            // Generate reset token
            $resetToken = generateRandomString(64);
            $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour

            // Update user with reset token
            $sql = "UPDATE users SET password_reset_token = ?, password_reset_expires = ? WHERE id = ?";
            $this->db->update($sql, [$resetToken, $expiresAt, $user['id']]);

            // Send reset email
            $this->sendPasswordResetEmail($email, $resetToken);

            return ['success' => true, 'message' => 'If the email exists, a password reset link has been sent.'];

        } catch (Exception $e) {
            logError("Password reset request error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Password reset request failed. Please try again.'];
        }
    }

    /**
     * Reset password
     */
    public function resetPassword($token, $newPassword) {
        try {
            // Validate password
            if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
                return ['success' => false, 'message' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long'];
            }

            // Find user with valid reset token
            $sql = "SELECT id FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()";
            $user = $this->db->fetchOne($sql, [$token]);

            if (!$user) {
                return ['success' => false, 'message' => 'Invalid or expired reset token'];
            }

            // Hash new password
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update password and clear reset token
            $sql = "UPDATE users SET password_hash = ?, password_reset_token = NULL, password_reset_expires = NULL WHERE id = ?";
            $this->db->update($sql, [$passwordHash, $user['id']]);

            return ['success' => true, 'message' => 'Password reset successfully! You can now log in with your new password.'];

        } catch (Exception $e) {
            logError("Password reset error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Password reset failed. Please try again.'];
        }
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        $sql = "SELECT id, first_name, last_name, email, theme_preference, profile_image FROM users WHERE id = ?";
        return $this->db->fetchOne($sql, [$_SESSION['user_id']]);
    }

    /**
     * Update user theme preference
     */
    public function updateThemePreference($userId, $theme) {
        $validThemes = ['light', 'dark', 'auto'];
        if (!in_array($theme, $validThemes)) {
            return false;
        }

        $sql = "UPDATE users SET theme_preference = ? WHERE id = ?";
        return $this->db->update($sql, [$theme, $userId]) > 0;
    }

    // Private helper methods

    private function validateRegistrationData($data) {
        $required = ['first_name', 'last_name', 'email', 'password'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => ucfirst(str_replace('_', ' ', $field)) . ' is required'];
            }
        }

        if (!isValidEmail($data['email'])) {
            return ['valid' => false, 'message' => 'Invalid email address'];
        }

        if (strlen($data['password']) < PASSWORD_MIN_LENGTH) {
            return ['valid' => false, 'message' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long'];
        }

        if (isset($data['confirm_password']) && $data['password'] !== $data['confirm_password']) {
            return ['valid' => false, 'message' => 'Passwords do not match'];
        }

        return ['valid' => true];
    }

    private function emailExists($email) {
        $sql = "SELECT id FROM users WHERE email = ?";
        $result = $this->db->fetchOne($sql, [$email]);
        return !empty($result);
    }

    private function getUserByEmail($email) {
        $sql = "SELECT * FROM users WHERE email = ?";
        return $this->db->fetchOne($sql, [$email]);
    }

    private function isAccountLocked($email) {
        // Implementation for account lockout logic
        $sql = "SELECT failed_attempts, last_failed_attempt FROM login_attempts WHERE email = ?";
        $attempts = $this->db->fetchOne($sql, [$email]);

        if (!$attempts) {
            return false;
        }

        if ($attempts['failed_attempts'] >= $this->maxLoginAttempts) {
            $lockoutEnd = strtotime($attempts['last_failed_attempt']) + $this->lockoutTime;
            return time() < $lockoutEnd;
        }

        return false;
    }

    private function recordFailedAttempt($email) {
        $sql = "INSERT INTO login_attempts (email, failed_attempts, last_failed_attempt)
                VALUES (?, 1, NOW())
                ON DUPLICATE KEY UPDATE
                failed_attempts = failed_attempts + 1,
                last_failed_attempt = NOW()";
        $this->db->execute($sql, [$email]);
    }

    private function clearFailedAttempts($email) {
        $sql = "DELETE FROM login_attempts WHERE email = ?";
        $this->db->execute($sql, [$email]);
    }

    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $this->db->update($sql, [$userId]);
    }

    private function createUserSession($user, $rememberMe = false) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['theme_preference'] = $user['theme_preference'];
        $_SESSION['login_time'] = time();

        // Regenerate session ID for security
        session_regenerate_id(true);

        if ($rememberMe) {
            $this->setRememberMeCookie($user['id']);
        }
    }

    private function setRememberMeCookie($userId) {
        $token = generateRandomString(64);
        $expires = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database
        $sql = "INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (?, ?, ?)";
        $this->db->insert($sql, [$userId, hash('sha256', $token), date('Y-m-d H:i:s', $expires)]);

        // Set cookie
        setcookie('remember_token', $token, $expires, '/', '', true, true);
    }

    private function sendVerificationEmail($email, $token) {
        try {
            $emailService = new EmailService();
            return $emailService->sendVerificationEmail($email, $token);
        } catch (Exception $e) {
            logError("Verification email error for $email: " . $e->getMessage());
            return false;
        }
    }

    private function sendPasswordResetEmail($email, $token) {
        try {
            $emailService = new EmailService();
            return $emailService->sendPasswordResetEmail($email, $token);
        } catch (Exception $e) {
            logError("Password reset email error for $email: " . $e->getMessage());
            return false;
        }
    }
}
?>
