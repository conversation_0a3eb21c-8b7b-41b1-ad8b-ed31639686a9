<?php
/**
 * Main Configuration File for OmniStore
 *
 * This file contains all the main configuration settings
 * for the OmniStore e-commerce platform.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone setting
date_default_timezone_set('Africa/Accra');

// Site configuration
define('SITE_NAME', 'OmniStore');
define('SITE_URL', 'http://localhost/omnistore');
define('SITE_DESCRIPTION', 'Your one-stop shop for everything');
define('SITE_KEYWORDS', 'ecommerce, online shopping, electronics, clothing, books');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('CONFIG_PATH', ROOT_PATH . '/config');

// URL paths
define('BASE_URL', SITE_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');
define('ADMIN_URL', BASE_URL . '/admin');
define('API_URL', BASE_URL . '/api');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('PRODUCT_IMAGE_WIDTH', 800);
define('PRODUCT_IMAGE_HEIGHT', 600);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 225);

// Pagination settings
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);
define('REVIEWS_PER_PAGE', 5);

// Currency and pricing
define('DEFAULT_CURRENCY', 'GHS');
define('CURRENCY_SYMBOL', '₵');
define('DECIMAL_PLACES', 2);

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'jjdu uvnm cuso xuxl');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'OmniStore');

// Paystack settings (Ghana)
define('PAYSTACK_PUBLIC_KEY', 'pk_test_your_public_key_here');
define('PAYSTACK_SECRET_KEY', 'sk_test_your_secret_key_here');
define('PAYSTACK_CURRENCY', 'GHS');
define('PAYSTACK_CALLBACK_URL', BASE_URL . '/api/paystack-callback.php');

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_DURATION', 3600); // 1 hour

// Debug settings
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('LOG_FILE', ROOT_PATH . '/logs/error.log');

// Theme settings
define('DEFAULT_THEME', 'auto'); // auto, light, dark
define('THEME_TRANSITION_DURATION', '0.3s');

// Include required files
require_once CONFIG_PATH . '/database.php';

/**
 * Autoloader for classes
 */
spl_autoload_register(function ($className) {
    $directories = [
        INCLUDES_PATH . '/classes/',
        INCLUDES_PATH . '/auth/',
        INCLUDES_PATH . '/utils/',
        INCLUDES_PATH . '/email/'
    ];

    foreach ($directories as $directory) {
        $file = $directory . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    return CURRENCY_SYMBOL . number_format($amount, DECIMAL_PLACES);
}

/**
 * Redirect function
 */
function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit();
}

/**
 * Get current URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

/**
 * Get user ID from session
 */
function getUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get admin ID from session
 */
function getAdminId() {
    return $_SESSION['admin_id'] ?? null;
}

/**
 * Log error message
 */
function logError($message, $file = '', $line = '') {
    if (LOG_ERRORS) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message";
        if ($file) $logMessage .= " in $file";
        if ($line) $logMessage .= " on line $line";
        $logMessage .= PHP_EOL;

        $logDir = dirname(LOG_FILE);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        error_log($logMessage, 3, LOG_FILE);
    }
}

/**
 * Debug function
 */
function debug($data, $die = false) {
    if (DEBUG_MODE) {
        echo '<pre>';
        print_r($data);
        echo '</pre>';
        if ($die) die();
    }
}

// Set error handler
set_error_handler(function($severity, $message, $file, $line) {
    logError($message, $file, $line);
});

// Set exception handler
set_exception_handler(function($exception) {
    logError($exception->getMessage(), $exception->getFile(), $exception->getLine());
    if (DEBUG_MODE) {
        echo "Uncaught exception: " . $exception->getMessage();
    } else {
        echo "An error occurred. Please try again later.";
    }
});
?>
