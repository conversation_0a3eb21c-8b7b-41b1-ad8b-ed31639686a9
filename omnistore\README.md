# OmniStore E-commerce Platform

## Project Overview
OmniStore is a comprehensive e-commerce platform built with vanilla PHP, MySQL, and modern frontend technologies. It features a complete admin panel, user authentication, payment integration with Paystack, and a dynamic theming system.

## Technology Stack
- **Backend**: Vanilla PHP (no frameworks)
- **Database**: MySQL
- **Frontend**: Tailwind CSS with custom CSS variables
- **JavaScript**: Vanilla JavaScript
- **Payment**: Paystack (Ghana - GHS currency)

## Project Structure
```
omnistore/
├── admin/                  # Admin panel
│   ├── assets/            # Admin-specific assets
│   ├── includes/          # Admin includes
│   ├── pages/             # Admin pages
│   └── index.php          # Admin dashboard
├── api/                   # API endpoints
├── assets/                # Frontend assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   ├── images/           # Images
│   └── fonts/            # Custom fonts
├── config/               # Configuration files
├── database/             # Database schema and migrations
├── includes/             # PHP includes
│   ├── auth/            # Authentication utilities
│   ├── email/           # Email templates and utilities
│   └── utils/           # General utilities
├── uploads/              # User uploaded files
│   ├── products/        # Product images
│   ├── users/           # User avatars
│   └── categories/      # Category images
└── index.php            # Main entry point
```

## Features

### Core Features
- [x] Project setup and folder structure
- [ ] User authentication system
- [ ] Product catalog with categories
- [ ] Shopping cart functionality
- [ ] Paystack payment integration
- [ ] Order management system
- [ ] Admin dashboard
- [ ] User account management
- [ ] Dynamic theming system

### Advanced Features
- [ ] Advanced search and filtering
- [ ] Product reviews and ratings
- [ ] Email notifications
- [ ] Inventory tracking
- [ ] Sales analytics
- [ ] Order tracking
- [ ] Multi-image product gallery

## Security Features
- Input validation and sanitization
- SQL injection prevention (prepared statements)
- XSS protection
- CSRF token implementation
- Secure password hashing
- Session management with timeout

## Installation Instructions
1. Clone/copy the project to your XAMPP www directory
2. Import the database schema from `/database/schema.sql`
3. Configure database connection in `/config/database.php`
4. Set up Paystack API keys in `/config/paystack.php`
5. Configure email settings in `/config/email.php`
6. Access the application at `http://localhost/omnistore`

## Development Timeline
See `TIMELINE.md` for detailed development schedule and progress tracking.

## Database Schema
See `database/schema.sql` for complete database structure.

## Contributing
This is a custom e-commerce solution. Follow PHP best practices and maintain code documentation.

## License
Custom proprietary license for OmniStore platform.
