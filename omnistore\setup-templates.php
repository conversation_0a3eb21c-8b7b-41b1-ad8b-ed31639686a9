<?php
/**
 * Setup Email Templates via Web Interface
 * 
 * This page creates the default email templates in the database.
 */

require_once 'config/config.php';

$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_templates'])) {
    try {
        $emailService = new EmailService();
        $emailService->createDefaultTemplates();
        
        $message = 'Email templates created successfully! The authentication system is now ready to send emails.';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'Error setting up email templates: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Check if templates already exist
try {
    $db = getDB();
    $templateCount = $db->fetchOne("SELECT COUNT(*) as count FROM email_templates");
    $templatesExist = $templateCount && $templateCount['count'] > 0;
} catch (Exception $e) {
    $templatesExist = false;
}

$pageTitle = "Setup Email Templates - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="text-2xl font-bold text-primary-600">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>
                <div class="text-sm text-secondary">
                    Email Templates Setup
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-2xl mx-auto">
                <div class="bg-surface rounded-lg shadow-lg p-8 border border-primary">
                    <h1 class="text-3xl font-bold text-primary mb-6">Email Templates Setup</h1>
                    
                    <!-- Message Display -->
                    <?php if (!empty($message)): ?>
                        <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-success-100 text-success-800 border border-success-200' : ($messageType === 'error' ? 'bg-error-100 text-error-800 border border-error-200' : 'bg-primary-100 text-primary-800 border border-primary-200'); ?>">
                            <div class="flex items-center">
                                <?php if ($messageType === 'success'): ?>
                                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                <?php elseif ($messageType === 'error'): ?>
                                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Status -->
                    <div class="mb-8 p-4 <?php echo $templatesExist ? 'bg-success-100 border border-success-200' : 'bg-warning-100 border border-warning-200'; ?> rounded-lg">
                        <div class="flex items-center <?php echo $templatesExist ? 'text-success-800' : 'text-warning-800'; ?>">
                            <?php if ($templatesExist): ?>
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Email templates are already set up (<?php echo $templateCount['count']; ?> templates found)</span>
                            <?php else: ?>
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <span>Email templates need to be set up for the authentication system to work</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Templates List -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-primary mb-4">Email Templates to be Created:</h2>
                        <ul class="space-y-2 text-sm text-secondary">
                            <li class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Email Verification Template
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Password Reset Template
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Welcome Email Template
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Order Confirmation Template
                            </li>
                            <li class="flex items-center">
                                <svg class="h-4 w-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Order Status Update Template
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Setup Form -->
                    <?php if (!$templatesExist || $messageType === 'error'): ?>
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="flex space-x-4">
                                <button type="submit" 
                                        name="setup_templates" 
                                        value="1" 
                                        class="btn btn-primary">
                                    <?php echo $templatesExist ? 'Recreate' : 'Create'; ?> Email Templates
                                </button>
                                
                                <a href="<?php echo BASE_URL; ?>" class="btn btn-secondary">
                                    Back to Homepage
                                </a>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="flex space-x-4">
                            <a href="<?php echo BASE_URL; ?>/test-email.php" class="btn btn-primary">
                                Test Email System
                            </a>
                            
                            <a href="<?php echo BASE_URL; ?>" class="btn btn-secondary">
                                Back to Homepage
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Instructions -->
                    <div class="mt-8 p-4 bg-tertiary rounded-lg">
                        <h3 class="text-lg font-semibold text-primary mb-2">Next Steps</h3>
                        <ol class="list-decimal list-inside text-sm text-secondary space-y-1">
                            <li>Set up email templates (this page)</li>
                            <li>Test email system using the test page</li>
                            <li>Try registering a new user account</li>
                            <li>Check your email for verification link</li>
                            <li>Complete the registration process</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
