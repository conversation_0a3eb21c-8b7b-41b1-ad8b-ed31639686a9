<?php
/**
 * User Logout API Endpoint
 * 
 * Handles user logout requests
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        exit;
    }
    
    // Log logout event
    Security::logSecurityEvent('user_logout', [
        'user_id' => getUserId()
    ]);
    
    // Create Auth instance and logout
    $auth = new Auth();
    $result = $auth->logout();
    
    echo json_encode([
        'success' => true,
        'message' => 'Logged out successfully',
        'redirect' => BASE_URL
    ]);
    
} catch (Exception $e) {
    logError("Logout API error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Logout failed. Please try again.']);
}
?>
