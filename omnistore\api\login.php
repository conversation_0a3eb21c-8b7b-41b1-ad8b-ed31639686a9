<?php
/**
 * User Login API Endpoint
 *
 * Handles user login requests via AJAX
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    // Verify CSRF token
    if (!isset($_POST[CSRF_TOKEN_NAME]) || !verifyCSRFToken($_POST[CSRF_TOKEN_NAME])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid security token']);
        exit;
    }

    // Rate limiting
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    if (!Security::checkRateLimit('login_' . $clientIP, 10, 900)) { // 10 attempts per 15 minutes
        http_response_code(429);
        echo json_encode(['success' => false, 'message' => 'Too many login attempts. Please try again later.']);
        exit;
    }

    // Sanitize input data
    $email = Security::sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']) && $_POST['remember_me'] === '1';

    // Validate required fields
    if (empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Email and password are required']);
        exit;
    }

    // Validate email format
    if (!Security::validateEmail($email)) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
        exit;
    }

    // Create Auth instance and attempt login
    $auth = new Auth();
    $result = $auth->login($email, $password, $rememberMe);

    if ($result['success']) {
        // Log successful login
        Security::logSecurityEvent('user_login', [
            'email' => $email,
            'user_id' => $result['user']['id'],
            'remember_me' => $rememberMe
        ]);

        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'action' => 'login',
            'user' => $result['user'],
            'csrf_token' => generateCSRFToken(),
            'redirect' => $_POST['redirect'] ?? BASE_URL
        ]);
    } else {
        // Log failed login attempt
        Security::logSecurityEvent('login_failed', [
            'email' => $email,
            'reason' => $result['message']
        ]);

        echo json_encode(['success' => false, 'message' => $result['message']]);
    }

} catch (Exception $e) {
    logError("Login API error: " . $e->getMessage());

    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Login failed. Please try again.']);
}
?>
