<?php
/**
 * Create Admin User Script
 * 
 * Run this script once to create an admin user
 * Remove this file after creating the admin user
 */

require_once 'config/config.php';

// Admin user details
$adminData = [
    'first_name' => 'Admin',
    'last_name' => 'User',
    'email' => '<EMAIL>',
    'password' => 'admin123', // Change this to a secure password
    'role' => 'admin'
];

try {
    $db = getDB();
    
    // Check if admin already exists
    $existingAdmin = $db->fetchOne(
        "SELECT id FROM users WHERE email = :email OR role = 'admin'",
        ['email' => $adminData['email']]
    );
    
    if ($existingAdmin) {
        echo "Admin user already exists!\n";
        exit;
    }
    
    // Hash password
    $passwordHash = password_hash($adminData['password'], PASSWORD_DEFAULT);
    
    // Create admin user
    $sql = "INSERT INTO users (
        id, first_name, last_name, email, password_hash, 
        email_verified, role, status, created_at, updated_at
    ) VALUES (
        :id, :first_name, :last_name, :email, :password_hash,
        1, :role, 'active', NOW(), NOW()
    )";
    
    $params = [
        'id' => generateUniqueId('USR', 16),
        'first_name' => $adminData['first_name'],
        'last_name' => $adminData['last_name'],
        'email' => $adminData['email'],
        'password_hash' => $passwordHash,
        'role' => $adminData['role']
    ];
    
    $db->execute($sql, $params);
    
    echo "Admin user created successfully!\n";
    echo "Email: " . $adminData['email'] . "\n";
    echo "Password: " . $adminData['password'] . "\n";
    echo "\nPlease change the password after first login.\n";
    echo "You can now access the admin panel at: " . BASE_URL . "/admin/\n";
    echo "\nRemember to delete this file (create-admin.php) for security!\n";
    
} catch (Exception $e) {
    echo "Error creating admin user: " . $e->getMessage() . "\n";
}
?>
