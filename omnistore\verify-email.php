<?php
/**
 * Email Verification Page
 * 
 * Handles email verification when users click the link in their email
 */

require_once 'config/config.php';

$message = '';
$messageType = 'info';

if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = Security::sanitizeInput($_GET['token']);
    
    $auth = new Auth();
    $result = $auth->verifyEmail($token);
    
    if ($result['success']) {
        $message = $result['message'];
        $messageType = 'success';
        
        // Log successful verification
        Security::logSecurityEvent('email_verified', ['token' => substr($token, 0, 10) . '...']);
    } else {
        $message = $result['message'];
        $messageType = 'error';
        
        // Log failed verification
        Security::logSecurityEvent('email_verification_failed', ['token' => substr($token, 0, 10) . '...']);
    }
} else {
    $message = 'Invalid verification link.';
    $messageType = 'error';
}

$pageTitle = "Email Verification - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="text-2xl font-bold text-primary-600">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen flex items-center justify-center">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-md mx-auto bg-surface rounded-lg shadow-lg p-8 border border-primary">
                <div class="text-center">
                    <!-- Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full mb-6 <?php echo $messageType === 'success' ? 'bg-success-100' : ($messageType === 'error' ? 'bg-error-100' : 'bg-primary-100'); ?>">
                        <?php if ($messageType === 'success'): ?>
                            <svg class="h-8 w-8 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        <?php elseif ($messageType === 'error'): ?>
                            <svg class="h-8 w-8 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        <?php else: ?>
                            <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Title -->
                    <h1 class="text-2xl font-bold text-primary mb-4">
                        <?php echo $messageType === 'success' ? 'Email Verified!' : 'Verification Failed'; ?>
                    </h1>
                    
                    <!-- Message -->
                    <p class="text-secondary mb-8">
                        <?php echo htmlspecialchars($message); ?>
                    </p>
                    
                    <!-- Actions -->
                    <div class="space-y-4">
                        <?php if ($messageType === 'success'): ?>
                            <button onclick="openLoginModal()" class="w-full btn btn-primary">
                                Login to Your Account
                            </button>
                        <?php else: ?>
                            <a href="<?php echo BASE_URL; ?>" class="w-full btn btn-secondary inline-block">
                                Return to Homepage
                            </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo BASE_URL; ?>" class="block text-center text-secondary hover:text-primary transition-colors">
                            Back to Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-secondary text-primary py-8 border-t border-primary">
        <div class="container mx-auto px-4 text-center">
            <p class="text-secondary">&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
