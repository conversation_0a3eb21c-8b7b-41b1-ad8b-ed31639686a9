[2025-05-28 12:01:10] Homepage data fetch error: Database connection failed. Please try again later.
[2025-05-28 12:02:31] Homepage data fetch error: Database connection failed. Please try again later.
[2025-05-28 12:58:26] SECURITY EVENT: {"timestamp":"2025-05-28 12:58:26","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Invalid email or password"}}
[2025-05-28 12:58:31] SECURITY EVENT: {"timestamp":"2025-05-28 12:58:31","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; <PERSON><PERSON>; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Invalid email or password"}}
[2025-05-28 13:00:24] Verification <NAME_EMAIL> with token: 26aaf0d1afe0d882a454adffac659a8e6b8ab68e8ca2dd5e06589a80a9a47744
[2025-05-28 13:00:24] SECURITY EVENT: {"timestamp":"2025-05-28 13:00:24","event":"user_registration","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","user_id":"1"}}
[2025-05-28 13:00:53] SECURITY EVENT: {"timestamp":"2025-05-28 13:00:53","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Please verify your email address before logging in."}}
[2025-05-28 13:33:36] SECURITY EVENT: {"timestamp":"2025-05-28 13:33:36","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Please verify your email address before logging in."}}
[2025-05-28 13:33:53] SECURITY EVENT: {"timestamp":"2025-05-28 13:33:53","event":"registration_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Email address already registered"}}
[2025-05-28 13:35:10] Template email error: Email template 'email_verification' not found
[2025-05-28 13:35:10] SECURITY EVENT: {"timestamp":"2025-05-28 13:35:10","event":"user_registration","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","user_id":"2"}}
[2025-05-28 13:35:23] SECURITY EVENT: {"timestamp":"2025-05-28 13:35:23","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","reason":"Please verify your email address before logging in."}}
[2025-05-28 13:48:18] Template email error: Email template 'email_verification' not found
[2025-05-28 13:48:18] SECURITY EVENT: {"timestamp":"2025-05-28 13:48:18","event":"user_registration","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","user_id":"3"}}
[2025-05-28 14:05:21] Attempting to send email via SMTP to: <EMAIL>
[2025-05-28 14:05:22] SMTP Initial response: 220 smtp.gmail.com ESMTP 5b1f17b1804b1-45006499b3dsm11585715e9.8 - gsmtp
[2025-05-28 14:05:24] SMTP EHLO response: 250-smtp.gmail.com at your service, [**************]
250-SIZE 35882577
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-28 14:05:24] SMTP STARTTLS response: 220 2.0.0 Ready to start TLS
[2025-05-28 14:05:24] SMTP EHLO after TLS response: 250-smtp.gmail.com at your service, [**************]
250-SIZE 35882577
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-28 14:05:25] SMTP AUTH LOGIN response: 334 VXNlcm5hbWU6
[2025-05-28 14:05:25] SMTP Username response: 334 UGFzc3dvcmQ6
[2025-05-28 14:05:25] SMTP Password response: 235 2.7.0 Accepted
[2025-05-28 14:05:25] SMTP MAIL FROM response: 250 2.1.0 OK 5b1f17b1804b1-45006499b3dsm11585715e9.8 - gsmtp
[2025-05-28 14:05:25] SMTP RCPT TO response: 250 2.1.5 OK 5b1f17b1804b1-45006499b3dsm11585715e9.8 - gsmtp
[2025-05-28 14:05:26] SMTP DATA response: 354 Go ahead 5b1f17b1804b1-45006499b3dsm11585715e9.8 - gsmtp
[2025-05-28 14:05:27] SMTP Final response: 250 2.0.0 OK  1748415928 5b1f17b1804b1-45006499b3dsm11585715e9.8 - gsmtp
[2025-05-28 14:05:27] SMTP email sent successfully to: <EMAIL>
[2025-05-28 14:05:27] Email sent successfully to: <EMAIL> with subject: OmniStore Email Test
[2025-05-28 14:07:25] Attempting to send email via SMTP to: <EMAIL>
[2025-05-28 14:07:26] SMTP Initial response: 220 smtp.gmail.com ESMTP ffacd0b85a97d-3a4eac7e159sm638683f8f.29 - gsmtp
[2025-05-28 14:07:27] SMTP EHLO response: 250-smtp.gmail.com at your service, [**************]
250-SIZE 35882577
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-28 14:07:27] SMTP STARTTLS response: 220 2.0.0 Ready to start TLS
[2025-05-28 14:07:28] SMTP EHLO after TLS response: 250-smtp.gmail.com at your service, [**************]
250-SIZE 35882577
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-28 14:07:29] SMTP AUTH LOGIN response: 334 VXNlcm5hbWU6
[2025-05-28 14:07:29] SMTP Username response: 334 UGFzc3dvcmQ6
[2025-05-28 14:07:29] SMTP Password response: 235 2.7.0 Accepted
[2025-05-28 14:07:30] SMTP MAIL FROM response: 250 2.1.0 OK ffacd0b85a97d-3a4eac7e159sm638683f8f.29 - gsmtp
[2025-05-28 14:07:30] SMTP RCPT TO response: 250 2.1.5 OK ffacd0b85a97d-3a4eac7e159sm638683f8f.29 - gsmtp
[2025-05-28 14:07:30] SMTP DATA response: 354 Go ahead ffacd0b85a97d-3a4eac7e159sm638683f8f.29 - gsmtp
[2025-05-28 14:07:31] SMTP Final response: 250 2.0.0 OK  1748416052 ffacd0b85a97d-3a4eac7e159sm638683f8f.29 - gsmtp
[2025-05-28 14:07:31] SMTP email sent successfully to: <EMAIL>
[2025-05-28 14:07:31] Email sent successfully to: <EMAIL> with subject: Verify your email address - OmniStore
[2025-05-28 14:07:31] SECURITY EVENT: {"timestamp":"2025-05-28 14:07:31","event":"user_registration","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"email":"<EMAIL>","user_id":"4"}}
[2025-05-28 14:07:53] SECURITY EVENT: {"timestamp":"2025-05-28 14:07:53","event":"email_verified","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"token":"3daba92a59..."}}
[2025-05-28 14:08:43] SECURITY EVENT: {"timestamp":"2025-05-28 14:08:43","event":"email_verification_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":null,"details":{"token":"3daba92a59..."}}
[2025-05-28 14:08:57] SECURITY EVENT: {"timestamp":"2025-05-28 14:08:57","event":"user_login","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"email":"<EMAIL>","user_id":4,"remember_me":true}}
[2025-05-28 14:10:01] SECURITY EVENT: {"timestamp":"2025-05-28 14:10:01","event":"email_verification_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"token":"3daba92a59..."}}
[2025-05-29 08:43:28] SECURITY EVENT: {"timestamp":"2025-05-29 08:43:28","event":"user_login","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"email":"<EMAIL>","user_id":4,"remember_me":true}}
[2025-05-29 08:45:23] SECURITY EVENT: {"timestamp":"2025-05-29 08:45:23","event":"login_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"email":"<EMAIL>","reason":"Invalid email or password"}}
[2025-05-29 08:46:05] Attempting to send email via SMTP to: <EMAIL>
[2025-05-29 08:46:07] SMTP Initial response: 220 smtp.gmail.com ESMTP ffacd0b85a97d-3a4efe6c866sm450603f8f.32 - gsmtp
[2025-05-29 08:46:07] SMTP EHLO response: 250-smtp.gmail.com at your service, [*************]
250-SIZE 35882577
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-29 08:46:08] SMTP STARTTLS response: 220 2.0.0 Ready to start TLS
[2025-05-29 08:46:09] SMTP EHLO after TLS response: 250-smtp.gmail.com at your service, [*************]
250-SIZE 35882577
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8
[2025-05-29 08:46:09] SMTP AUTH LOGIN response: 334 VXNlcm5hbWU6
[2025-05-29 08:46:10] SMTP Username response: 334 UGFzc3dvcmQ6
[2025-05-29 08:46:10] SMTP Password response: 235 2.7.0 Accepted
[2025-05-29 08:46:11] SMTP MAIL FROM response: 250 2.1.0 OK ffacd0b85a97d-3a4efe6c866sm450603f8f.32 - gsmtp
[2025-05-29 08:46:11] SMTP RCPT TO response: 250 2.1.5 OK ffacd0b85a97d-3a4efe6c866sm450603f8f.32 - gsmtp
[2025-05-29 08:46:12] SMTP DATA response: 354 Go ahead ffacd0b85a97d-3a4efe6c866sm450603f8f.32 - gsmtp
[2025-05-29 08:46:16] SMTP Final response: 250 2.0.0 OK  1748483174 ffacd0b85a97d-3a4efe6c866sm450603f8f.32 - gsmtp
[2025-05-29 08:46:16] SMTP email sent successfully to: <EMAIL>
[2025-05-29 08:46:16] Email sent successfully to: <EMAIL> with subject: Verify your email address - OmniStore
[2025-05-29 08:46:16] SECURITY EVENT: {"timestamp":"2025-05-29 08:46:16","event":"user_registration","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"email":"<EMAIL>","user_id":"5"}}
[2025-05-29 08:46:43] SECURITY EVENT: {"timestamp":"2025-05-29 08:46:43","event":"email_verified","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"token":"d079140402..."}}
[2025-05-29 08:47:07] SECURITY EVENT: {"timestamp":"2025-05-29 08:47:07","event":"email_verification_failed","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":4,"details":{"token":"d079140402..."}}
[2025-05-29 08:47:25] SECURITY EVENT: {"timestamp":"2025-05-29 08:47:25","event":"user_login","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"email":"<EMAIL>","user_id":5,"remember_me":true}}
[2025-05-29 09:07:08] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:08","event":"user_logout","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"user_id":5}}
[2025-05-29 09:07:10] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:10","event":"user_logout","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"user_id":5}}
[2025-05-29 09:07:12] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:12","event":"user_logout","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"user_id":5}}
[2025-05-29 09:07:26] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:26","event":"user_login","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"email":"<EMAIL>","user_id":5,"remember_me":true}}
[2025-05-29 09:07:36] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:36","event":"user_logout","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"user_id":5}}
[2025-05-29 09:07:49] SECURITY EVENT: {"timestamp":"2025-05-29 09:07:49","event":"user_login","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","user_id":5,"details":{"email":"<EMAIL>","user_id":5,"remember_me":false}}
