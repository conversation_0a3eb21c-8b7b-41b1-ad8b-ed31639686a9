<?php
/**
 * Logout Page
 * 
 * Handles user logout and redirects to homepage
 */

require_once 'config/config.php';

try {
    // Check if user is logged in
    if (isLoggedIn()) {
        // Log logout event
        Security::logSecurityEvent('user_logout', [
            'user_id' => getUserId()
        ]);
        
        // Create Auth instance and logout
        $auth = new Auth();
        $auth->logout();
    }
    
    // Redirect to homepage
    redirect(BASE_URL);
    
} catch (Exception $e) {
    logError("Logout page error: " . $e->getMessage());
    redirect(BASE_URL);
}
?>
