<?php
/**
 * Email Service for OmniStore
 *
 * Handles email sending, templates, and notifications
 * using PHP's built-in mail function with proper headers.
 */

class EmailService {
    private $db;
    private $fromEmail;
    private $fromName;

    public function __construct() {
        $this->db = getDB();
        $this->fromEmail = FROM_EMAIL;
        $this->fromName = FROM_NAME;
    }

    /**
     * Send email using template
     */
    public function sendTemplateEmail($to, $templateName, $variables = []) {
        try {
            // Get email template
            $template = $this->getEmailTemplate($templateName);
            if (!$template) {
                throw new Exception("Email template '$templateName' not found");
            }

            // Replace variables in template
            $subject = $this->replaceVariables($template['subject'], $variables);
            $body = $this->replaceVariables($template['body'], $variables);

            return $this->sendEmail($to, $subject, $body);

        } catch (Exception $e) {
            logError("Template email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send plain email
     */
    public function sendEmail($to, $subject, $body, $isHtml = true) {
        try {
            // Validate email address
            if (!Security::validateEmail($to)) {
                throw new Exception("Invalid email address: $to");
            }

            // Try SMTP first, fallback to mail()
            $success = $this->sendViaSMTP($to, $subject, $body, $isHtml);

            if (!$success) {
                // Fallback to PHP mail()
                $headers = $this->buildHeaders($isHtml);
                $success = mail($to, $subject, $body, $headers);
            }

            if ($success) {
                $this->logEmailSent($to, $subject);
                return true;
            } else {
                throw new Exception("Failed to send email to $to");
            }

        } catch (Exception $e) {
            logError("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send verification email
     */
    public function sendVerificationEmail($email, $token) {
        $verificationUrl = SITE_URL . "/verify-email.php?token=" . urlencode($token);

        $variables = [
            'site_name' => SITE_NAME,
            'verification_url' => $verificationUrl,
            'email' => $email
        ];

        return $this->sendTemplateEmail($email, 'email_verification', $variables);
    }

    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($email, $token) {
        $resetUrl = SITE_URL . "/reset-password.php?token=" . urlencode($token);

        $variables = [
            'site_name' => SITE_NAME,
            'reset_url' => $resetUrl,
            'email' => $email
        ];

        return $this->sendTemplateEmail($email, 'password_reset', $variables);
    }

    /**
     * Send welcome email
     */
    public function sendWelcomeEmail($email, $firstName) {
        $variables = [
            'site_name' => SITE_NAME,
            'first_name' => $firstName,
            'login_url' => SITE_URL . '/login.php',
            'support_email' => FROM_EMAIL
        ];

        return $this->sendTemplateEmail($email, 'welcome', $variables);
    }

    /**
     * Send order confirmation email
     */
    public function sendOrderConfirmationEmail($email, $orderData) {
        $variables = [
            'site_name' => SITE_NAME,
            'order_number' => $orderData['order_number'],
            'customer_name' => $orderData['customer_name'],
            'order_total' => formatCurrency($orderData['total_amount']),
            'order_items' => $this->formatOrderItems($orderData['items']),
            'order_url' => SITE_URL . '/order-details.php?id=' . $orderData['id']
        ];

        return $this->sendTemplateEmail($email, 'order_confirmation', $variables);
    }

    /**
     * Send order status update email
     */
    public function sendOrderStatusEmail($email, $orderData, $newStatus) {
        $variables = [
            'site_name' => SITE_NAME,
            'order_number' => $orderData['order_number'],
            'customer_name' => $orderData['customer_name'],
            'new_status' => ucfirst($newStatus),
            'order_url' => SITE_URL . '/order-details.php?id=' . $orderData['id']
        ];

        return $this->sendTemplateEmail($email, 'order_status_update', $variables);
    }

    /**
     * Get email template from database
     */
    private function getEmailTemplate($name) {
        $sql = "SELECT * FROM email_templates WHERE name = ? AND is_active = 1";
        return $this->db->fetchOne($sql, [$name]);
    }

    /**
     * Replace variables in template content
     */
    private function replaceVariables($content, $variables) {
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        return $content;
    }

    /**
     * Build email headers
     */
    private function buildHeaders($isHtml = true) {
        $headers = [];

        // From header
        $headers[] = "From: {$this->fromName} <{$this->fromEmail}>";

        // Reply-To header
        $headers[] = "Reply-To: {$this->fromEmail}";

        // Content type
        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }

        // Additional headers
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "X-Mailer: OmniStore Mailer";
        $headers[] = "X-Priority: 3";

        return implode("\r\n", $headers);
    }

    /**
     * Format order items for email
     */
    private function formatOrderItems($items) {
        $formatted = "<table style='width: 100%; border-collapse: collapse;'>";
        $formatted .= "<tr style='background-color: #f8f9fa;'>";
        $formatted .= "<th style='padding: 10px; text-align: left; border: 1px solid #dee2e6;'>Product</th>";
        $formatted .= "<th style='padding: 10px; text-align: center; border: 1px solid #dee2e6;'>Quantity</th>";
        $formatted .= "<th style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>Price</th>";
        $formatted .= "<th style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>Total</th>";
        $formatted .= "</tr>";

        foreach ($items as $item) {
            $formatted .= "<tr>";
            $formatted .= "<td style='padding: 10px; border: 1px solid #dee2e6;'>" . Security::escapeHtml($item['product_name']) . "</td>";
            $formatted .= "<td style='padding: 10px; text-align: center; border: 1px solid #dee2e6;'>" . $item['quantity'] . "</td>";
            $formatted .= "<td style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>" . formatCurrency($item['unit_price']) . "</td>";
            $formatted .= "<td style='padding: 10px; text-align: right; border: 1px solid #dee2e6;'>" . formatCurrency($item['total_price']) . "</td>";
            $formatted .= "</tr>";
        }

        $formatted .= "</table>";
        return $formatted;
    }

    /**
     * Log sent email
     */
    private function logEmailSent($to, $subject) {
        try {
            $sql = "INSERT INTO email_log (recipient, subject, sent_at) VALUES (?, ?, NOW())";
            $this->db->insert($sql, [$to, $subject]);
        } catch (Exception $e) {
            // Don't throw error for logging failure
            logError("Email log error: " . $e->getMessage());
        }
    }

    /**
     * Create default email templates
     */
    public function createDefaultTemplates() {
        $templates = [
            [
                'name' => 'email_verification',
                'subject' => 'Verify your email address - {{site_name}}',
                'body' => $this->getVerificationTemplate(),
                'variables' => json_encode(['site_name', 'verification_url', 'email'])
            ],
            [
                'name' => 'password_reset',
                'subject' => 'Reset your password - {{site_name}}',
                'body' => $this->getPasswordResetTemplate(),
                'variables' => json_encode(['site_name', 'reset_url', 'email'])
            ],
            [
                'name' => 'welcome',
                'subject' => 'Welcome to {{site_name}}!',
                'body' => $this->getWelcomeTemplate(),
                'variables' => json_encode(['site_name', 'first_name', 'login_url', 'support_email'])
            ],
            [
                'name' => 'order_confirmation',
                'subject' => 'Order Confirmation #{{order_number}} - {{site_name}}',
                'body' => $this->getOrderConfirmationTemplate(),
                'variables' => json_encode(['site_name', 'order_number', 'customer_name', 'order_total', 'order_items', 'order_url'])
            ],
            [
                'name' => 'order_status_update',
                'subject' => 'Order #{{order_number}} Status Update - {{site_name}}',
                'body' => $this->getOrderStatusTemplate(),
                'variables' => json_encode(['site_name', 'order_number', 'customer_name', 'new_status', 'order_url'])
            ]
        ];

        foreach ($templates as $template) {
            $sql = "INSERT IGNORE INTO email_templates (name, subject, body, variables, is_active) VALUES (?, ?, ?, ?, 1)";
            $this->db->insert($sql, [$template['name'], $template['subject'], $template['body'], $template['variables']]);
        }
    }

    // Email template methods

    private function getVerificationTemplate() {
        return '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2563eb;">Verify Your Email Address</h2>
                <p>Hello,</p>
                <p>Thank you for registering with {{site_name}}! To complete your registration, please verify your email address by clicking the button below:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{verification_url}}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Email Address</a>
                </div>
                <p>If the button doesn\'t work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{{verification_url}}</p>
                <p>This verification link will expire in 24 hours.</p>
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="font-size: 12px; color: #666;">
                    If you didn\'t create an account with {{site_name}}, you can safely ignore this email.
                </p>
            </div>
        </body>
        </html>';
    }

    private function getPasswordResetTemplate() {
        return '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2563eb;">Reset Your Password</h2>
                <p>Hello,</p>
                <p>We received a request to reset your password for your {{site_name}} account. Click the button below to reset your password:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{reset_url}}" style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
                </div>
                <p>If the button doesn\'t work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{{reset_url}}</p>
                <p>This password reset link will expire in 1 hour.</p>
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="font-size: 12px; color: #666;">
                    If you didn\'t request a password reset, you can safely ignore this email. Your password will not be changed.
                </p>
            </div>
        </body>
        </html>';
    }

    private function getWelcomeTemplate() {
        return '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2563eb;">Welcome to {{site_name}}!</h2>
                <p>Hello {{first_name}},</p>
                <p>Welcome to {{site_name}}! We\'re excited to have you as part of our community.</p>
                <p>Your account has been successfully created and verified. You can now:</p>
                <ul>
                    <li>Browse our extensive product catalog</li>
                    <li>Add items to your cart and wishlist</li>
                    <li>Track your orders</li>
                    <li>Manage your account preferences</li>
                </ul>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{login_url}}" style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Start Shopping</a>
                </div>
                <p>If you have any questions, feel free to contact our support team at {{support_email}}.</p>
                <p>Happy shopping!</p>
                <p>The {{site_name}} Team</p>
            </div>
        </body>
        </html>';
    }

    private function getOrderConfirmationTemplate() {
        return '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2563eb;">Order Confirmation #{{order_number}}</h2>
                <p>Hello {{customer_name}},</p>
                <p>Thank you for your order! We\'ve received your order and it\'s being processed.</p>
                <h3>Order Details:</h3>
                {{order_items}}
                <div style="text-align: right; margin: 20px 0; font-size: 18px; font-weight: bold;">
                    Total: {{order_total}}
                </div>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{order_url}}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Order Details</a>
                </div>
                <p>We\'ll send you another email when your order ships.</p>
                <p>Thank you for shopping with {{site_name}}!</p>
            </div>
        </body>
        </html>';
    }

    private function getOrderStatusTemplate() {
        return '
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2563eb;">Order Status Update</h2>
                <p>Hello {{customer_name}},</p>
                <p>Your order #{{order_number}} status has been updated to: <strong>{{new_status}}</strong></p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{order_url}}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Order Details</a>
                </div>
                <p>Thank you for shopping with {{site_name}}!</p>
            </div>
        </body>
        </html>';
    }

    /**
     * Send email via SMTP
     */
    private function sendViaSMTP($to, $subject, $body, $isHtml = true) {
        try {
            // Create socket connection
            $socket = fsockopen(SMTP_HOST, SMTP_PORT, $errno, $errstr, 30);
            if (!$socket) {
                throw new Exception("Could not connect to SMTP server: $errstr ($errno)");
            }

            // Read initial response
            $this->readSMTPResponse($socket);

            // Send EHLO
            fwrite($socket, "EHLO " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "\r\n");
            $this->readSMTPResponse($socket);

            // Start TLS if required
            if (SMTP_ENCRYPTION === 'tls') {
                fwrite($socket, "STARTTLS\r\n");
                $this->readSMTPResponse($socket);

                if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    throw new Exception("Failed to enable TLS encryption");
                }

                // Send EHLO again after TLS
                fwrite($socket, "EHLO " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "\r\n");
                $this->readSMTPResponse($socket);
            }

            // Authenticate
            fwrite($socket, "AUTH LOGIN\r\n");
            $this->readSMTPResponse($socket);

            fwrite($socket, base64_encode(SMTP_USERNAME) . "\r\n");
            $this->readSMTPResponse($socket);

            fwrite($socket, base64_encode(SMTP_PASSWORD) . "\r\n");
            $this->readSMTPResponse($socket);

            // Send email
            fwrite($socket, "MAIL FROM: <" . $this->fromEmail . ">\r\n");
            $this->readSMTPResponse($socket);

            fwrite($socket, "RCPT TO: <$to>\r\n");
            $this->readSMTPResponse($socket);

            fwrite($socket, "DATA\r\n");
            $this->readSMTPResponse($socket);

            // Email headers and body
            $headers = $this->buildSMTPHeaders($to, $subject, $isHtml);
            fwrite($socket, $headers . "\r\n\r\n" . $body . "\r\n.\r\n");
            $this->readSMTPResponse($socket);

            // Quit
            fwrite($socket, "QUIT\r\n");
            fclose($socket);

            return true;

        } catch (Exception $e) {
            logError("SMTP sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Read SMTP response
     */
    private function readSMTPResponse($socket) {
        $response = '';
        while ($line = fgets($socket, 515)) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') {
                break;
            }
        }
        return $response;
    }

    /**
     * Build SMTP headers
     */
    private function buildSMTPHeaders($to, $subject, $isHtml = true) {
        $headers = [];

        $headers[] = "To: $to";
        $headers[] = "From: {$this->fromName} <{$this->fromEmail}>";
        $headers[] = "Reply-To: {$this->fromEmail}";
        $headers[] = "Subject: $subject";
        $headers[] = "Date: " . date('r');
        $headers[] = "Message-ID: <" . uniqid() . "@" . ($_SERVER['SERVER_NAME'] ?? 'localhost') . ">";

        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }

        $headers[] = "MIME-Version: 1.0";
        $headers[] = "X-Mailer: OmniStore Mailer";
        $headers[] = "X-Priority: 3";

        return implode("\r\n", $headers);
    }
}
?>
