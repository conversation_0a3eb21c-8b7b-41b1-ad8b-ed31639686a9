/**
 * Admin Panel JavaScript
 * 
 * Handles admin-specific functionality and interactions
 */

// Admin state
let adminState = {
    currentPage: 1,
    itemsPerPage: 20,
    sortBy: 'created_at',
    sortOrder: 'desc',
    filters: {}
};

/**
 * Initialize admin panel
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize admin functionality
 */
function initializeAdmin() {
    // Initialize data tables
    initializeDataTables();
    
    // Initialize image uploads
    initializeImageUploads();
    
    // Initialize form handlers
    initializeFormHandlers();
    
    // Initialize search and filters
    initializeSearchFilters();
    
    // Initialize tooltips
    initializeTooltips();
}

/**
 * Initialize data tables
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        // Add sorting functionality
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const sortBy = header.dataset.sort;
                handleSort(sortBy);
            });
        });
        
        // Add row selection
        const checkboxes = table.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleRowSelection);
        });
    });
}

/**
 * Handle table sorting
 */
function handleSort(sortBy) {
    if (adminState.sortBy === sortBy) {
        adminState.sortOrder = adminState.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        adminState.sortBy = sortBy;
        adminState.sortOrder = 'asc';
    }
    
    // Reload data with new sorting
    reloadTableData();
}

/**
 * Handle row selection
 */
function handleRowSelection(event) {
    const checkbox = event.target;
    const row = checkbox.closest('tr');
    
    if (checkbox.checked) {
        row.classList.add('selected');
    } else {
        row.classList.remove('selected');
    }
    
    // Update bulk actions
    updateBulkActions();
}

/**
 * Update bulk actions based on selection
 */
function updateBulkActions() {
    const selectedRows = document.querySelectorAll('.data-table tr.selected');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        if (selectedRows.length > 0) {
            bulkActions.style.display = 'flex';
            bulkActions.querySelector('.selected-count').textContent = selectedRows.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

/**
 * Initialize image upload functionality
 */
function initializeImageUploads() {
    const uploadAreas = document.querySelectorAll('.image-upload');
    
    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        
        // Click to upload
        area.addEventListener('click', () => {
            if (input) input.click();
        });
        
        // Drag and drop
        area.addEventListener('dragover', (e) => {
            e.preventDefault();
            area.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', () => {
            area.classList.remove('dragover');
        });
        
        area.addEventListener('drop', (e) => {
            e.preventDefault();
            area.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (input && files.length > 0) {
                input.files = files;
                handleImageUpload(input);
            }
        });
        
        // File input change
        if (input) {
            input.addEventListener('change', () => {
                handleImageUpload(input);
            });
        }
    });
}

/**
 * Handle image upload
 */
function handleImageUpload(input) {
    const files = input.files;
    const previewContainer = input.closest('.image-upload').nextElementSibling;
    
    if (!previewContainer || !previewContainer.classList.contains('image-preview')) {
        return;
    }
    
    Array.from(files).forEach(file => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                createImagePreview(e.target.result, file.name, previewContainer);
            };
            reader.readAsDataURL(file);
        }
    });
}

/**
 * Create image preview
 */
function createImagePreview(src, name, container) {
    const previewItem = document.createElement('div');
    previewItem.className = 'image-preview-item';
    
    previewItem.innerHTML = `
        <img src="${src}" alt="${name}">
        <button type="button" class="remove-btn" onclick="removeImagePreview(this)">×</button>
        <div class="image-info">
            <p class="text-xs text-secondary truncate">${name}</p>
        </div>
    `;
    
    container.appendChild(previewItem);
}

/**
 * Remove image preview
 */
function removeImagePreview(button) {
    const previewItem = button.closest('.image-preview-item');
    if (previewItem) {
        previewItem.remove();
    }
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    const forms = document.querySelectorAll('.admin-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
    });
    
    // Auto-save functionality
    const autoSaveForms = document.querySelectorAll('[data-auto-save]');
    autoSaveForms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                debounce(() => autoSaveForm(form), 1000)();
            });
        });
    });
}

/**
 * Handle form submission
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Saving...';
    
    try {
        const formData = new FormData(form);
        const response = await fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message || 'Saved successfully', 'success');
            
            // Handle redirects
            if (result.redirect) {
                setTimeout(() => {
                    window.location.href = result.redirect;
                }, 1000);
            }
        } else {
            showNotification(result.message || 'An error occurred', 'error');
        }
        
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification('An error occurred while saving', 'error');
    } finally {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

/**
 * Auto-save form
 */
async function autoSaveForm(form) {
    try {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Auto-saved', 'info', 2000);
        }
        
    } catch (error) {
        console.error('Auto-save error:', error);
    }
}

/**
 * Initialize search and filters
 */
function initializeSearchFilters() {
    const searchInput = document.querySelector('.search-input input');
    const filterSelects = document.querySelectorAll('.filter-select select');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(() => {
            adminState.filters.search = searchInput.value;
            reloadTableData();
        }, 500));
    }
    
    filterSelects.forEach(select => {
        select.addEventListener('change', () => {
            const filterName = select.name;
            const filterValue = select.value;
            
            if (filterValue) {
                adminState.filters[filterName] = filterValue;
            } else {
                delete adminState.filters[filterName];
            }
            
            reloadTableData();
        });
    });
}

/**
 * Reload table data
 */
async function reloadTableData() {
    const tableContainer = document.querySelector('.data-table-container');
    if (!tableContainer) return;
    
    // Show loading state
    showLoadingOverlay();
    
    try {
        const params = new URLSearchParams({
            page: adminState.currentPage,
            limit: adminState.itemsPerPage,
            sort_by: adminState.sortBy,
            sort_order: adminState.sortOrder,
            ...adminState.filters
        });
        
        const response = await fetch(`${window.location.pathname}?${params}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const html = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newTable = doc.querySelector('.data-table-container');
        
        if (newTable) {
            tableContainer.innerHTML = newTable.innerHTML;
            initializeDataTables();
        }
        
    } catch (error) {
        console.error('Table reload error:', error);
        showNotification('Failed to reload data', 'error');
    } finally {
        hideLoadingOverlay();
    }
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    let overlay = document.querySelector('.loading-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(overlay);
    }
    overlay.style.display = 'flex';
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Show tooltip
 */
function showTooltip(event) {
    const element = event.target;
    const text = element.dataset.tooltip;
    
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    element._tooltip = tooltip;
}

/**
 * Hide tooltip
 */
function hideTooltip(event) {
    const element = event.target;
    if (element._tooltip) {
        element._tooltip.remove();
        delete element._tooltip;
    }
}

/**
 * Confirm deletion
 */
function confirmDelete(message = 'Are you sure you want to delete this item?') {
    return confirm(message);
}

/**
 * Bulk delete
 */
async function bulkDelete(endpoint) {
    const selectedRows = document.querySelectorAll('.data-table tr.selected');
    const ids = Array.from(selectedRows).map(row => row.dataset.id).filter(Boolean);
    
    if (ids.length === 0) {
        showNotification('No items selected', 'warning');
        return;
    }
    
    if (!confirm(`Are you sure you want to delete ${ids.length} item(s)?`)) {
        return;
    }
    
    try {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ ids })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message || 'Items deleted successfully', 'success');
            reloadTableData();
        } else {
            showNotification(result.message || 'Failed to delete items', 'error');
        }
        
    } catch (error) {
        console.error('Bulk delete error:', error);
        showNotification('An error occurred while deleting', 'error');
    }
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Format currency
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
}

// Export functions for global use
window.adminUtils = {
    confirmDelete,
    bulkDelete,
    reloadTableData,
    formatCurrency,
    formatDate,
    showLoadingOverlay,
    hideLoadingOverlay
};
