<?php
/**
 * User Registration API Endpoint
 * 
 * Handles user registration requests via AJAX
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    // Verify CSRF token
    if (!isset($_POST[CSRF_TOKEN_NAME]) || !verifyCSRFToken($_POST[CSRF_TOKEN_NAME])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid security token']);
        exit;
    }
    
    // Rate limiting
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    if (!Security::checkRateLimit('register_' . $clientIP, 5, 3600)) {
        http_response_code(429);
        echo json_encode(['success' => false, 'message' => 'Too many registration attempts. Please try again later.']);
        exit;
    }
    
    // Sanitize input data
    $data = [
        'first_name' => Security::sanitizeInput($_POST['first_name'] ?? ''),
        'last_name' => Security::sanitizeInput($_POST['last_name'] ?? ''),
        'email' => Security::sanitizeInput($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'theme_preference' => Security::sanitizeInput($_POST['theme_preference'] ?? 'auto'),
        'agree_terms' => isset($_POST['agree_terms']) && $_POST['agree_terms'] === '1'
    ];
    
    // Validate required fields
    $requiredFields = ['first_name', 'last_name', 'email', 'password'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            echo json_encode(['success' => false, 'message' => ucfirst(str_replace('_', ' ', $field)) . ' is required']);
            exit;
        }
    }
    
    // Validate email
    if (!Security::validateEmail($data['email'])) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
        exit;
    }
    
    // Validate password
    $passwordValidation = Security::validatePassword($data['password']);
    if (!$passwordValidation['valid']) {
        echo json_encode(['success' => false, 'message' => implode('. ', $passwordValidation['errors'])]);
        exit;
    }
    
    // Check password confirmation
    if ($data['password'] !== $data['confirm_password']) {
        echo json_encode(['success' => false, 'message' => 'Passwords do not match']);
        exit;
    }
    
    // Check terms agreement
    if (!$data['agree_terms']) {
        echo json_encode(['success' => false, 'message' => 'You must agree to the Terms of Service and Privacy Policy']);
        exit;
    }
    
    // Validate theme preference
    $validThemes = ['light', 'dark', 'auto'];
    if (!in_array($data['theme_preference'], $validThemes)) {
        $data['theme_preference'] = 'auto';
    }
    
    // Create Auth instance and register user
    $auth = new Auth();
    $result = $auth->register($data);
    
    if ($result['success']) {
        // Log security event
        Security::logSecurityEvent('user_registration', [
            'email' => $data['email'],
            'user_id' => $result['user_id'] ?? null
        ]);
        
        // Send welcome email (after verification)
        $emailService = new EmailService();
        // Note: Welcome email will be sent after email verification
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'action' => 'register'
        ]);
    } else {
        // Log failed registration attempt
        Security::logSecurityEvent('registration_failed', [
            'email' => $data['email'],
            'reason' => $result['message']
        ]);
        
        echo json_encode(['success' => false, 'message' => $result['message']]);
    }
    
} catch (Exception $e) {
    logError("Registration API error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Registration failed. Please try again.']);
}
?>
