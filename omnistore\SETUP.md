# OmniStore Setup Guide

## Quick Start Instructions

### 1. Database Setup
1. Open phpMyAdmin or your MySQL client
2. Import the database schema:
   ```sql
   -- Navigate to: http://localhost/phpmyadmin
   -- Create new database or import: database/schema.sql
   ```
3. The schema will create:
   - Database: `omnistore`
   - Default admin user: `<EMAIL>` (password: `admin123`)
   - Sample categories (Electronics, Clothing, etc.)

### 2. Configuration
1. Update database credentials in `config/database.php` if needed:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'omnistore');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

2. Configure Paystack keys in `config/config.php`:
   ```php
   define('PAYSTACK_PUBLIC_KEY', 'your_public_key_here');
   define('PAYSTACK_SECRET_KEY', 'your_secret_key_here');
   ```

### 3. File Permissions
Ensure these directories are writable:
```bash
chmod 755 uploads/
chmod 755 uploads/products/
chmod 755 uploads/users/
chmod 755 uploads/categories/
```

### 4. Access the Platform
- **Frontend**: http://localhost/omnistore/
- **Admin Panel**: http://localhost/omnistore/admin/ (coming in Phase 8)

## What's Working Now

### ✅ Completed Features
- **Homepage**: Modern, responsive design with hero section
- **Theming**: Dynamic light/dark mode with auto-detection
- **Navigation**: Responsive navbar with category dropdown
- **Modals**: Login/register modals (UI ready, backend in Phase 3)
- **Cart**: Frontend cart functionality with localStorage
- **Search**: Search bar with placeholder functionality
- **Database**: Complete schema with all required tables

### 🎨 Theming System
The theming system is fully functional:
- **Auto Mode**: Detects system preference or uses time-based fallback
- **Manual Override**: Users can manually select light/dark themes
- **Smooth Transitions**: All theme changes are animated
- **Persistent**: Theme preference saved in localStorage

### 📱 Responsive Design
- Mobile-first approach
- Breakpoints: 480px, 768px, 1024px
- Touch-friendly interface
- Optimized for all screen sizes

## Next Development Steps

### Phase 2: Core Infrastructure (Days 2-3)
1. **Authentication System**
   - User registration/login backend
   - Email verification
   - Password reset functionality

2. **Security Implementation**
   - CSRF protection
   - Input validation
   - XSS prevention

3. **Email System**
   - SMTP configuration
   - Email templates
   - Notification system

### Phase 3: Product Management (Days 4-8)
1. **Admin Panel**
   - Product CRUD operations
   - Category management
   - Image upload system

2. **Frontend Integration**
   - Product listing pages
   - Product detail pages
   - Search and filtering

## Testing the Current Setup

### 1. Homepage Test
Visit `http://localhost/omnistore/` and verify:
- [ ] Page loads without errors
- [ ] Theme toggle works (look for theme button)
- [ ] Navigation is responsive
- [ ] Hero section displays correctly
- [ ] Categories section shows placeholder content

### 2. Theme System Test
- [ ] Click theme toggle button (should cycle: auto → light → dark)
- [ ] Refresh page (theme should persist)
- [ ] Change system theme (auto mode should follow)

### 3. Modal System Test
- [ ] Click login button (modal should open)
- [ ] Click outside modal (should close)
- [ ] Press Escape key (should close)
- [ ] Switch between login/register modals

### 4. Database Test
Run this query to verify setup:
```sql
SELECT COUNT(*) as categories FROM categories;
SELECT COUNT(*) as admin_users FROM admin_users;
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check XAMPP MySQL is running
   - Verify database credentials in `config/database.php`
   - Ensure `omnistore` database exists

2. **CSS/JS Not Loading**
   - Check file paths in `config/config.php`
   - Verify XAMPP is serving from correct directory
   - Clear browser cache

3. **Theme Not Working**
   - Check browser console for JavaScript errors
   - Ensure `assets/js/theme.js` is loading
   - Verify CSS custom properties support

4. **Responsive Issues**
   - Test in browser dev tools
   - Check Tailwind CSS is loading
   - Verify viewport meta tag

## File Structure Overview

```
omnistore/
├── index.php              # Homepage (✅ Complete)
├── config/
│   ├── config.php         # Main configuration (✅ Complete)
│   └── database.php       # Database connection (✅ Complete)
├── database/
│   └── schema.sql         # Database schema (✅ Complete)
├── assets/
│   ├── css/
│   │   ├── theme.css      # Theming system (✅ Complete)
│   │   └── main.css       # Main styles (✅ Complete)
│   └── js/
│       ├── theme.js       # Theme management (✅ Complete)
│       └── main.js        # Core functionality (✅ Complete)
└── [Other directories prepared for future phases]
```

## Development Progress

- **Phase 1**: ✅ Complete (Project setup, theming, homepage)
- **Phase 2**: 🔄 Next (Authentication, security, email)
- **Phase 3**: ⏳ Planned (Product management, admin panel)

## Support

For development questions or issues:
1. Check the `PROGRESS.md` file for current status
2. Review `TIMELINE.md` for planned features
3. Examine browser console for JavaScript errors
4. Check PHP error logs for backend issues

---

**Ready to continue with Phase 2!** 🚀

The foundation is solid and ready for the next development phase. All core systems (theming, configuration, database schema) are in place and tested.
