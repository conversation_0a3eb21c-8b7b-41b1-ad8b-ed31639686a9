/**
 * OmniStore Theme Management System
 * 
 * Handles dynamic theme switching with the following features:
 * - Auto-detection based on system preference
 * - Time-based fallback (light during day, dark at night)
 * - User override with localStorage persistence
 * - Smooth transitions between themes
 */

class ThemeManager {
    constructor() {
        this.themes = ['light', 'dark', 'auto'];
        this.currentTheme = this.getStoredTheme() || 'auto';
        this.init();
    }

    /**
     * Initialize theme manager
     */
    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
        this.createThemeToggle();
        
        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                if (this.currentTheme === 'auto') {
                    this.applyAutoTheme();
                }
            });
        }
    }

    /**
     * Get stored theme from localStorage
     */
    getStoredTheme() {
        try {
            return localStorage.getItem('omnistore-theme');
        } catch (e) {
            console.warn('localStorage not available for theme storage');
            return null;
        }
    }

    /**
     * Store theme in localStorage
     */
    storeTheme(theme) {
        try {
            localStorage.setItem('omnistore-theme', theme);
        } catch (e) {
            console.warn('localStorage not available for theme storage');
        }
    }

    /**
     * Apply theme to document
     */
    applyTheme(theme) {
        this.currentTheme = theme;
        
        if (theme === 'auto') {
            this.applyAutoTheme();
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
        
        this.storeTheme(theme);
        this.updateThemeToggle();
        this.dispatchThemeChangeEvent(theme);
    }

    /**
     * Apply auto theme based on system preference or time
     */
    applyAutoTheme() {
        let effectiveTheme = 'light';
        
        // Check system preference first
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            effectiveTheme = 'dark';
        } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
            effectiveTheme = 'light';
        } else {
            // Fallback to time-based theme
            effectiveTheme = this.getTimeBasedTheme();
        }
        
        document.documentElement.setAttribute('data-theme', effectiveTheme);
        this.dispatchThemeChangeEvent('auto', effectiveTheme);
    }

    /**
     * Get theme based on current time
     * Light: 6 AM - 7 PM
     * Dark: 7 PM - 6 AM
     */
    getTimeBasedTheme() {
        const hour = new Date().getHours();
        return (hour >= 6 && hour < 19) ? 'light' : 'dark';
    }

    /**
     * Toggle between themes
     */
    toggleTheme() {
        const currentIndex = this.themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % this.themes.length;
        this.applyTheme(this.themes[nextIndex]);
    }

    /**
     * Set specific theme
     */
    setTheme(theme) {
        if (this.themes.includes(theme)) {
            this.applyTheme(theme);
        }
    }

    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get effective theme (resolved auto theme)
     */
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return document.documentElement.getAttribute('data-theme') || 'light';
        }
        return this.currentTheme;
    }

    /**
     * Create theme toggle button
     */
    createThemeToggle() {
        // Check if toggle already exists
        if (document.getElementById('theme-toggle')) {
            return;
        }

        const toggle = document.createElement('button');
        toggle.id = 'theme-toggle';
        toggle.className = 'theme-toggle';
        toggle.setAttribute('aria-label', 'Toggle theme');
        toggle.innerHTML = this.getThemeIcon();
        
        // Add click event
        toggle.addEventListener('click', () => this.toggleTheme());
        
        // Add to navigation or create floating button
        const nav = document.querySelector('.navbar') || document.querySelector('nav');
        if (nav) {
            nav.appendChild(toggle);
        } else {
            // Create floating theme toggle
            toggle.classList.add('theme-toggle-floating');
            document.body.appendChild(toggle);
        }
    }

    /**
     * Get theme icon based on current theme
     */
    getThemeIcon() {
        const icons = {
            light: '☀️',
            dark: '🌙',
            auto: '🌓'
        };
        return icons[this.currentTheme] || icons.auto;
    }

    /**
     * Update theme toggle appearance
     */
    updateThemeToggle() {
        const toggle = document.getElementById('theme-toggle');
        if (toggle) {
            toggle.innerHTML = this.getThemeIcon();
            toggle.setAttribute('title', `Current theme: ${this.currentTheme}`);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for theme change events from other parts of the app
        document.addEventListener('themeChange', (e) => {
            if (e.detail.theme !== this.currentTheme) {
                this.applyTheme(e.detail.theme);
            }
        });

        // Update auto theme every hour
        setInterval(() => {
            if (this.currentTheme === 'auto') {
                this.applyAutoTheme();
            }
        }, 60 * 60 * 1000); // 1 hour
    }

    /**
     * Dispatch theme change event
     */
    dispatchThemeChangeEvent(theme, effectiveTheme = null) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: theme,
                effectiveTheme: effectiveTheme || theme,
                timestamp: new Date().toISOString()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Add CSS for theme toggle
     */
    addThemeToggleStyles() {
        if (document.getElementById('theme-toggle-styles')) {
            return;
        }

        const styles = document.createElement('style');
        styles.id = 'theme-toggle-styles';
        styles.textContent = `
            .theme-toggle {
                background: var(--surface);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-md);
                padding: var(--spacing-sm);
                cursor: pointer;
                font-size: 1.2rem;
                transition: all var(--transition-fast);
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 40px;
                min-height: 40px;
            }
            
            .theme-toggle:hover {
                background: var(--surface-hover);
                transform: scale(1.05);
            }
            
            .theme-toggle:active {
                transform: scale(0.95);
            }
            
            .theme-toggle-floating {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                box-shadow: var(--shadow-lg);
            }
            
            @media (max-width: 768px) {
                .theme-toggle-floating {
                    bottom: 15px;
                    right: 15px;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
    window.themeManager.addThemeToggleStyles();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
