/**
 * OmniStore Theme Management System
 *
 * Handles dynamic theme switching with the following features:
 * - Auto-detection based on system preference
 * - Time-based fallback (light during day, dark at night)
 * - User override with localStorage persistence
 * - Smooth transitions between themes
 */

class ThemeManager {
    constructor() {
        this.themes = ['light', 'dark', 'auto'];
        this.currentTheme = this.getStoredTheme() || 'auto';
        this.init();
    }

    /**
     * Initialize theme manager
     */
    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
        this.createThemeToggle();

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                if (this.currentTheme === 'auto') {
                    this.applyAutoTheme();
                }
            });
        }
    }

    /**
     * Get stored theme from localStorage
     */
    getStoredTheme() {
        try {
            return localStorage.getItem('omnistore-theme');
        } catch (e) {
            console.warn('localStorage not available for theme storage');
            return null;
        }
    }

    /**
     * Store theme in localStorage
     */
    storeTheme(theme) {
        try {
            localStorage.setItem('omnistore-theme', theme);
        } catch (e) {
            console.warn('localStorage not available for theme storage');
        }
    }

    /**
     * Apply theme to document
     */
    applyTheme(theme) {
        this.currentTheme = theme;

        if (theme === 'auto') {
            this.applyAutoTheme();
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }

        this.storeTheme(theme);
        this.updateThemeToggle();
        this.dispatchThemeChangeEvent(theme);
    }

    /**
     * Apply auto theme based on system preference or time
     */
    applyAutoTheme() {
        let effectiveTheme = 'light';

        // Check system preference first
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            effectiveTheme = 'dark';
        } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
            effectiveTheme = 'light';
        } else {
            // Fallback to time-based theme
            effectiveTheme = this.getTimeBasedTheme();
        }

        document.documentElement.setAttribute('data-theme', effectiveTheme);
        this.dispatchThemeChangeEvent('auto', effectiveTheme);
    }

    /**
     * Get theme based on current time
     * Light: 6 AM - 7 PM
     * Dark: 7 PM - 6 AM
     */
    getTimeBasedTheme() {
        const hour = new Date().getHours();
        return (hour >= 6 && hour < 19) ? 'light' : 'dark';
    }

    /**
     * Toggle between themes
     */
    toggleTheme() {
        const currentIndex = this.themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % this.themes.length;
        this.applyTheme(this.themes[nextIndex]);
    }

    /**
     * Set specific theme
     */
    setTheme(theme) {
        if (this.themes.includes(theme)) {
            this.applyTheme(theme);
        }
    }

    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get effective theme (resolved auto theme)
     */
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return document.documentElement.getAttribute('data-theme') || 'light';
        }
        return this.currentTheme;
    }

    /**
     * Create theme toggle button
     */
    createThemeToggle() {
        // Check if toggle already exists
        if (document.getElementById('theme-toggle')) {
            return;
        }

        const toggle = document.createElement('button');
        toggle.id = 'theme-toggle';
        toggle.className = 'theme-toggle text-secondary hover:text-primary transition-colors';
        toggle.setAttribute('aria-label', 'Toggle theme');
        toggle.innerHTML = this.getThemeIcon();

        // Add click event
        toggle.addEventListener('click', () => this.toggleTheme());

        // Add to navigation icons container
        const navIcons = document.querySelector('.flex.items-center.space-x-4');
        if (navIcons) {
            // Insert before the mobile menu button (last child)
            const mobileMenuButton = navIcons.querySelector('button.md\\:hidden');
            if (mobileMenuButton) {
                navIcons.insertBefore(toggle, mobileMenuButton);
            } else {
                navIcons.appendChild(toggle);
            }
        } else {
            // Fallback: create floating button
            toggle.classList.add('theme-toggle-floating');
            document.body.appendChild(toggle);
        }
    }

    /**
     * Get theme icon based on current theme
     */
    getThemeIcon() {
        const icons = {
            light: `<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>`,
            dark: `<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                   </svg>`,
            auto: `<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                   </svg>`
        };
        return icons[this.currentTheme] || icons.auto;
    }

    /**
     * Update theme toggle appearance
     */
    updateThemeToggle() {
        const toggle = document.getElementById('theme-toggle');
        if (toggle) {
            toggle.innerHTML = this.getThemeIcon();
            toggle.setAttribute('title', `Current theme: ${this.currentTheme}`);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for theme change events from other parts of the app
        document.addEventListener('themeChange', (e) => {
            if (e.detail.theme !== this.currentTheme) {
                this.applyTheme(e.detail.theme);
            }
        });

        // Update auto theme every hour
        setInterval(() => {
            if (this.currentTheme === 'auto') {
                this.applyAutoTheme();
            }
        }, 60 * 60 * 1000); // 1 hour
    }

    /**
     * Dispatch theme change event
     */
    dispatchThemeChangeEvent(theme, effectiveTheme = null) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: theme,
                effectiveTheme: effectiveTheme || theme,
                timestamp: new Date().toISOString()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Add CSS for theme toggle
     */
    addThemeToggleStyles() {
        if (document.getElementById('theme-toggle-styles')) {
            return;
        }

        const styles = document.createElement('style');
        styles.id = 'theme-toggle-styles';
        styles.textContent = `
            .theme-toggle {
                background: transparent;
                border: none;
                cursor: pointer;
                padding: var(--spacing-xs);
                border-radius: var(--radius-md);
                transition: all var(--transition-fast);
                display: flex;
                align-items: center;
                justify-content: center;
                color: inherit;
            }

            .theme-toggle:hover {
                background: var(--surface-hover);
                transform: scale(1.05);
            }

            .theme-toggle:active {
                transform: scale(0.95);
            }

            .theme-toggle svg {
                color: inherit;
            }

            .theme-toggle-floating {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                box-shadow: var(--shadow-lg);
                background: var(--surface);
                border: 1px solid var(--border-primary);
                padding: var(--spacing-sm);
                min-width: 40px;
                min-height: 40px;
            }

            @media (max-width: 768px) {
                .theme-toggle-floating {
                    bottom: 15px;
                    right: 15px;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
    window.themeManager.addThemeToggleStyles();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
