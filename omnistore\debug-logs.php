<?php
/**
 * Debug Logs Viewer
 * 
 * Simple page to view error logs for debugging email issues
 * Remove this file in production
 */

require_once 'config/config.php';

// Only allow in debug mode
if (!DEBUG_MODE) {
    header('HTTP/1.0 404 Not Found');
    exit('Page not found');
}

$logFile = ROOT_PATH . '/logs/error.log';
$logs = [];

if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $logLines = array_reverse(explode("\n", $logContent));
    
    // Get last 50 log entries
    $logs = array_slice(array_filter($logLines), 0, 50);
}

// Clear logs if requested
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['clear_logs'])) {
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
        $logs = [];
    }
}

$pageTitle = "Debug Logs - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
    
    <style>
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="text-2xl font-bold text-primary-600">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>
                <div class="text-sm text-secondary">
                    Debug Logs Viewer
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-6xl mx-auto">
                <div class="bg-surface rounded-lg shadow-lg p-8 border border-primary">
                    <div class="flex items-center justify-between mb-6">
                        <h1 class="text-3xl font-bold text-primary">Debug Logs</h1>
                        
                        <div class="flex space-x-4">
                            <button onclick="location.reload()" class="btn btn-secondary">
                                Refresh
                            </button>
                            
                            <form method="POST" class="inline">
                                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo generateCSRFToken(); ?>">
                                <button type="submit" name="clear_logs" value="1" class="btn btn-primary" 
                                        onclick="return confirm('Are you sure you want to clear all logs?')">
                                    Clear Logs
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Log File Info -->
                    <div class="mb-6 p-4 bg-secondary rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <strong>Log File:</strong> <?php echo htmlspecialchars($logFile); ?>
                            </div>
                            <div>
                                <strong>File Exists:</strong> <?php echo file_exists($logFile) ? 'Yes' : 'No'; ?>
                            </div>
                            <div>
                                <strong>Total Entries:</strong> <?php echo count($logs); ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email Configuration -->
                    <div class="mb-6 p-4 bg-tertiary rounded-lg">
                        <h2 class="text-lg font-semibold text-primary mb-4">Current Email Configuration</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <strong>SMTP Host:</strong> <?php echo htmlspecialchars(SMTP_HOST); ?>
                            </div>
                            <div>
                                <strong>SMTP Port:</strong> <?php echo htmlspecialchars(SMTP_PORT); ?>
                            </div>
                            <div>
                                <strong>Username:</strong> <?php echo htmlspecialchars(SMTP_USERNAME); ?>
                            </div>
                            <div>
                                <strong>Encryption:</strong> <?php echo htmlspecialchars(SMTP_ENCRYPTION); ?>
                            </div>
                            <div>
                                <strong>From Email:</strong> <?php echo htmlspecialchars(FROM_EMAIL); ?>
                            </div>
                            <div>
                                <strong>From Name:</strong> <?php echo htmlspecialchars(FROM_NAME); ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Logs Display -->
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-primary mb-4">Recent Log Entries (Last 50)</h2>
                        
                        <?php if (empty($logs)): ?>
                            <div class="p-4 bg-warning-100 border border-warning-200 rounded-lg text-warning-800">
                                No log entries found. Try performing an action (like sending a test email) to generate logs.
                            </div>
                        <?php else: ?>
                            <div class="bg-gray-900 text-green-400 p-4 rounded-lg max-h-96 overflow-y-auto">
                                <?php foreach ($logs as $log): ?>
                                    <?php if (trim($log)): ?>
                                        <div class="log-entry mb-1 <?php echo strpos($log, 'SMTP') !== false ? 'text-blue-400' : (strpos($log, 'error') !== false ? 'text-red-400' : 'text-green-400'); ?>">
                                            <?php echo htmlspecialchars($log); ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="flex space-x-4">
                        <a href="<?php echo BASE_URL; ?>/test-email.php" class="btn btn-primary">
                            Test Email System
                        </a>
                        
                        <a href="<?php echo BASE_URL; ?>/setup-templates.php" class="btn btn-secondary">
                            Setup Templates
                        </a>
                        
                        <a href="<?php echo BASE_URL; ?>" class="btn btn-secondary">
                            Back to Homepage
                        </a>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="mt-8 p-4 bg-warning-100 border border-warning-200 rounded-lg">
                        <div class="flex items-center text-warning-800">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-sm">
                                <strong>Security Note:</strong> This debug page should only be used in development. Remove this file in production environment.
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <script>
        // Auto-refresh every 10 seconds
        setTimeout(function() {
            location.reload();
        }, 10000);
    </script>
</body>
</html>
