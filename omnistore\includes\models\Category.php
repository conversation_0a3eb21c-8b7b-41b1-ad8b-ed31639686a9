<?php
/**
 * Category Model
 * 
 * Handles all category-related database operations and business logic
 */

class Category {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Create a new category
     */
    public function create($data) {
        try {
            // Validate required fields
            if (empty($data['name'])) {
                throw new Exception("Category name is required");
            }
            
            // Generate slug if not provided
            $slug = !empty($data['slug']) ? $data['slug'] : $this->generateSlug($data['name']);
            
            // Sanitize and validate data
            $categoryData = [
                'id' => generateUniqueId('CAT', 16),
                'name' => Security::sanitizeInput($data['name']),
                'slug' => $this->generateUniqueSlug($slug),
                'description' => Security::sanitizeInput($data['description'] ?? ''),
                'parent_id' => !empty($data['parent_id']) ? intval($data['parent_id']) : null,
                'image_url' => Security::sanitizeInput($data['image_url'] ?? ''),
                'icon' => Security::sanitizeInput($data['icon'] ?? ''),
                'sort_order' => intval($data['sort_order'] ?? 0),
                'is_active' => !empty($data['is_active']) ? 1 : 0,
                'meta_title' => Security::sanitizeInput($data['meta_title'] ?? ''),
                'meta_description' => Security::sanitizeInput($data['meta_description'] ?? ''),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Validate parent category if specified
            if ($categoryData['parent_id']) {
                $parent = $this->getById($categoryData['parent_id']);
                if (!$parent) {
                    throw new Exception("Parent category not found");
                }
                if ($parent['parent_id']) {
                    throw new Exception("Cannot create subcategory of a subcategory (max 2 levels)");
                }
            }
            
            // Insert category
            $sql = "INSERT INTO categories (" . implode(', ', array_keys($categoryData)) . ") 
                    VALUES (:" . implode(', :', array_keys($categoryData)) . ")";
            
            $this->db->execute($sql, $categoryData);
            
            // Log category creation
            logError("Category created: " . $categoryData['name'] . " (ID: " . $categoryData['id'] . ")");
            
            return [
                'success' => true,
                'message' => 'Category created successfully',
                'category_id' => $categoryData['id']
            ];
            
        } catch (Exception $e) {
            logError("Category creation error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get category by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT c.*, 
                           p.name as parent_name,
                           (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
                    FROM categories c 
                    LEFT JOIN categories p ON c.parent_id = p.id 
                    WHERE c.id = :id";
            
            $category = $this->db->fetchOne($sql, ['id' => $id]);
            
            if ($category) {
                // Get subcategories
                $category['subcategories'] = $this->getSubcategories($id);
            }
            
            return $category;
            
        } catch (Exception $e) {
            logError("Get category error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get category by slug
     */
    public function getBySlug($slug) {
        try {
            $sql = "SELECT c.*, 
                           p.name as parent_name,
                           (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
                    FROM categories c 
                    LEFT JOIN categories p ON c.parent_id = p.id 
                    WHERE c.slug = :slug";
            
            $category = $this->db->fetchOne($sql, ['slug' => $slug]);
            
            if ($category) {
                // Get subcategories
                $category['subcategories'] = $this->getSubcategories($category['id']);
            }
            
            return $category;
            
        } catch (Exception $e) {
            logError("Get category by slug error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Update category
     */
    public function update($id, $data) {
        try {
            // Check if category exists
            $existing = $this->getById($id);
            if (!$existing) {
                throw new Exception("Category not found");
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = [
                'name', 'slug', 'description', 'parent_id', 'image_url', 
                'icon', 'sort_order', 'is_active', 'meta_title', 'meta_description'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    switch ($field) {
                        case 'parent_id':
                        case 'sort_order':
                            $updateData[$field] = !empty($data[$field]) ? intval($data[$field]) : null;
                            break;
                        case 'is_active':
                            $updateData[$field] = !empty($data[$field]) ? 1 : 0;
                            break;
                        case 'slug':
                            if ($data[$field] !== $existing['slug']) {
                                $updateData[$field] = $this->generateUniqueSlug($data[$field]);
                            }
                            break;
                        default:
                            $updateData[$field] = Security::sanitizeInput($data[$field]);
                    }
                }
            }
            
            // Validate parent category if being updated
            if (isset($updateData['parent_id']) && $updateData['parent_id']) {
                if ($updateData['parent_id'] == $id) {
                    throw new Exception("Category cannot be its own parent");
                }
                
                $parent = $this->getById($updateData['parent_id']);
                if (!$parent) {
                    throw new Exception("Parent category not found");
                }
                if ($parent['parent_id']) {
                    throw new Exception("Cannot create subcategory of a subcategory (max 2 levels)");
                }
            }
            
            if (empty($updateData)) {
                return [
                    'success' => true,
                    'message' => 'No changes to update'
                ];
            }
            
            // Add updated timestamp
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            // Build update query
            $setParts = [];
            foreach ($updateData as $field => $value) {
                $setParts[] = "$field = :$field";
            }
            
            $sql = "UPDATE categories SET " . implode(', ', $setParts) . " WHERE id = :id";
            $updateData['id'] = $id;
            
            $this->db->execute($sql, $updateData);
            
            // Log category update
            logError("Category updated: " . ($updateData['name'] ?? $existing['name']) . " (ID: $id)");
            
            return [
                'success' => true,
                'message' => 'Category updated successfully'
            ];
            
        } catch (Exception $e) {
            logError("Category update error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete category
     */
    public function delete($id) {
        try {
            // Check if category exists
            $category = $this->getById($id);
            if (!$category) {
                throw new Exception("Category not found");
            }
            
            // Check if category has products
            $productCount = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM products WHERE category_id = :id",
                ['id' => $id]
            );
            
            if ($productCount['count'] > 0) {
                throw new Exception("Cannot delete category with products. Move or delete products first.");
            }
            
            // Check if category has subcategories
            $subcategoryCount = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM categories WHERE parent_id = :id",
                ['id' => $id]
            );
            
            if ($subcategoryCount['count'] > 0) {
                throw new Exception("Cannot delete category with subcategories. Delete subcategories first.");
            }
            
            // Delete the category
            $sql = "DELETE FROM categories WHERE id = :id";
            $this->db->execute($sql, ['id' => $id]);
            
            // Log category deletion
            logError("Category deleted: " . $category['name'] . " (ID: $id)");
            
            return [
                'success' => true,
                'message' => 'Category deleted successfully'
            ];
            
        } catch (Exception $e) {
            logError("Category deletion error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get all categories with hierarchy
     */
    public function getAll($options = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Active filter
            if (isset($options['is_active'])) {
                $where[] = 'c.is_active = :is_active';
                $params['is_active'] = $options['is_active'] ? 1 : 0;
            }
            
            // Parent filter
            if (isset($options['parent_id'])) {
                if ($options['parent_id'] === null) {
                    $where[] = 'c.parent_id IS NULL';
                } else {
                    $where[] = 'c.parent_id = :parent_id';
                    $params['parent_id'] = $options['parent_id'];
                }
            }
            
            $whereClause = implode(' AND ', $where);
            $orderBy = $options['order_by'] ?? 'c.sort_order ASC, c.name ASC';
            
            $sql = "SELECT c.*, 
                           p.name as parent_name,
                           (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
                    FROM categories c 
                    LEFT JOIN categories p ON c.parent_id = p.id 
                    WHERE $whereClause 
                    ORDER BY $orderBy";
            
            $categories = $this->db->fetchAll($sql, $params);
            
            // Build hierarchy if requested
            if (!empty($options['hierarchy'])) {
                $categories = $this->buildHierarchy($categories);
            }
            
            return [
                'success' => true,
                'categories' => $categories
            ];
            
        } catch (Exception $e) {
            logError("Get categories error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'categories' => []
            ];
        }
    }
    
    /**
     * Get subcategories
     */
    public function getSubcategories($parentId) {
        try {
            $sql = "SELECT c.*, 
                           (SELECT COUNT(*) FROM products WHERE category_id = c.id AND is_active = 1) as product_count
                    FROM categories c 
                    WHERE c.parent_id = :parent_id AND c.is_active = 1
                    ORDER BY c.sort_order ASC, c.name ASC";
            
            return $this->db->fetchAll($sql, ['parent_id' => $parentId]);
            
        } catch (Exception $e) {
            logError("Get subcategories error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Generate slug from name
     */
    private function generateSlug($name) {
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }
    
    /**
     * Generate unique slug
     */
    private function generateUniqueSlug($baseSlug) {
        $slug = $this->generateSlug($baseSlug);
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Check if slug exists
     */
    private function slugExists($slug) {
        $result = $this->db->fetchOne("SELECT id FROM categories WHERE slug = :slug", ['slug' => $slug]);
        return !empty($result);
    }
    
    /**
     * Build category hierarchy
     */
    private function buildHierarchy($categories) {
        $hierarchy = [];
        $lookup = [];
        
        // Create lookup array
        foreach ($categories as $category) {
            $lookup[$category['id']] = $category;
            $lookup[$category['id']]['children'] = [];
        }
        
        // Build hierarchy
        foreach ($categories as $category) {
            if ($category['parent_id'] === null) {
                $hierarchy[] = &$lookup[$category['id']];
            } else {
                if (isset($lookup[$category['parent_id']])) {
                    $lookup[$category['parent_id']]['children'][] = &$lookup[$category['id']];
                }
            }
        }
        
        return $hierarchy;
    }
}
?>
