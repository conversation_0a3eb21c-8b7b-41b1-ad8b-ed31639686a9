<?php
/**
 * CSRF Token API Endpoint
 * 
 * Returns a fresh CSRF token for AJAX requests
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Generate fresh CSRF token
    $token = generateCSRFToken();
    
    echo json_encode([
        'success' => true,
        'token' => $token
    ]);
    
} catch (Exception $e) {
    logError("CSRF token API error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to generate token']);
}
?>
