/**
 * OmniStore Main JavaScript
 *
 * Contains core functionality for the OmniStore e-commerce platform
 * including cart management, modals, and user interactions.
 */

// Global variables
let cart = JSON.parse(localStorage.getItem('omnistore-cart')) || [];
let isLoggedIn = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    updateCartCount();
    checkAuthStatus();
});

/**
 * Initialize the application
 */
function initializeApp() {
    // Setup event listeners
    setupEventListeners();

    // Initialize modals
    initializeModals();

    // Setup search functionality
    setupSearch();

    // Setup mobile menu
    setupMobileMenu();

    console.log('OmniStore initialized successfully');
}

/**
 * Setup global event listeners
 */
function setupEventListeners() {
    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-overlay')) {
            closeModal(e.target.closest('.modal'));
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.active');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });

    // Handle form submissions
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('ajax-form')) {
            e.preventDefault();
            handleAjaxForm(e.target);
        }
    });
}

/**
 * Cart Management Functions
 */

/**
 * Add item to cart
 */
function addToCart(productId, quantity = 1) {
    // Check if product already exists in cart
    const existingItem = cart.find(item => item.productId === productId);

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            productId: productId,
            quantity: quantity,
            addedAt: new Date().toISOString()
        });
    }

    // Save to localStorage
    localStorage.setItem('omnistore-cart', JSON.stringify(cart));

    // Update cart count
    updateCartCount();

    // Show success message
    showNotification('Product added to cart!', 'success');

    // Sync with server if user is logged in
    if (isLoggedIn) {
        syncCartWithServer();
    }
}

/**
 * Remove item from cart
 */
function removeFromCart(productId) {
    cart = cart.filter(item => item.productId !== productId);
    localStorage.setItem('omnistore-cart', JSON.stringify(cart));
    updateCartCount();

    if (isLoggedIn) {
        syncCartWithServer();
    }
}

/**
 * Update cart item quantity
 */
function updateCartQuantity(productId, quantity) {
    const item = cart.find(item => item.productId === productId);
    if (item) {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            item.quantity = quantity;
            localStorage.setItem('omnistore-cart', JSON.stringify(cart));
            updateCartCount();

            if (isLoggedIn) {
                syncCartWithServer();
            }
        }
    }
}

/**
 * Update cart count display
 */
function updateCartCount() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const cartCountElement = document.getElementById('cart-count');
    if (cartCountElement) {
        cartCountElement.textContent = totalItems;
        cartCountElement.style.display = totalItems > 0 ? 'flex' : 'none';
    }
}

/**
 * Sync cart with server
 */
async function syncCartWithServer() {
    try {
        const response = await fetch('/api/cart-sync.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ cart: cart })
        });

        if (!response.ok) {
            throw new Error('Cart sync failed');
        }

        const result = await response.json();
        if (result.success) {
            console.log('Cart synced with server');
        }
    } catch (error) {
        console.error('Cart sync error:', error);
    }
}

/**
 * Modal Functions
 */

/**
 * Initialize modals
 */
function initializeModals() {
    // Create modal container if it doesn't exist
    if (!document.getElementById('modal-container')) {
        const modalContainer = document.createElement('div');
        modalContainer.id = 'modal-container';
        document.body.appendChild(modalContainer);
    }
}

/**
 * Open login modal
 */
function openLoginModal() {
    const csrfToken = generateCSRFToken();
    const modalHtml = `
        <div class="modal active" id="login-modal">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Login to Your Account</h2>
                    <button onclick="closeModal(document.getElementById('login-modal'))" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form class="ajax-form" action="/omnistore/api/login.php" method="POST">
                        <input type="hidden" name="csrf_token" value="${csrfToken}">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required class="form-input">
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember_me" value="1">
                                <span>Remember me</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-full">Login</button>
                    </form>
                    <div class="modal-footer">
                        <p>Don't have an account? <a href="#" onclick="openRegisterModal()">Register here</a></p>
                        <p><a href="#" onclick="openForgotPasswordModal()">Forgot your password?</a></p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

/**
 * Open register modal
 */
function openRegisterModal() {
    const csrfToken = generateCSRFToken();
    const modalHtml = `
        <div class="modal active" id="register-modal">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Create Your Account</h2>
                    <button onclick="closeModal(document.getElementById('register-modal'))" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form class="ajax-form" action="/omnistore/api/register.php" method="POST">
                        <input type="hidden" name="csrf_token" value="${csrfToken}">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name">First Name</label>
                                <input type="text" id="first_name" name="first_name" required class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="last_name">Last Name</label>
                                <input type="text" id="last_name" name="last_name" required class="form-input">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="reg_email">Email Address</label>
                            <input type="email" id="reg_email" name="email" required class="form-input">
                        </div>
                        <div class="form-group">
                            <label for="reg_password">Password</label>
                            <input type="password" id="reg_password" name="password" required class="form-input" minlength="8">
                            <small class="text-tertiary">Must be at least 8 characters with uppercase, lowercase, number, and special character</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Confirm Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" required class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="agree_terms" value="1" required>
                                <span>I agree to the <a href="/omnistore/terms.php" target="_blank">Terms of Service</a> and <a href="/omnistore/privacy.php" target="_blank">Privacy Policy</a></span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-full">Create Account</button>
                    </form>
                    <div class="modal-footer">
                        <p>Already have an account? <a href="#" onclick="openLoginModal()">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

/**
 * Close modal
 */
function closeModal(modal) {
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

/**
 * Search Functions
 */

/**
 * Setup search functionality
 */
function setupSearch() {
    const searchInput = document.querySelector('input[placeholder="Search products..."]');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(e.target.value);
            }, 300);
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                window.location.href = `/products.php?search=${encodeURIComponent(e.target.value)}`;
            }
        });
    }
}

/**
 * Perform search with suggestions
 */
async function performSearch(query) {
    if (query.length < 2) return;

    try {
        const response = await fetch(`/api/search-suggestions.php?q=${encodeURIComponent(query)}`);
        const suggestions = await response.json();

        // Display search suggestions
        displaySearchSuggestions(suggestions);
    } catch (error) {
        console.error('Search error:', error);
    }
}

/**
 * Mobile Menu Functions
 */

/**
 * Setup mobile menu
 */
function setupMobileMenu() {
    // Mobile menu will be implemented here
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
    // Mobile menu toggle functionality
    console.log('Mobile menu toggled');
}

/**
 * Utility Functions
 */

/**
 * Generate CSRF token (get from server)
 */
function generateCSRFToken() {
    // Try to get existing token from a meta tag or make a request
    let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!token) {
        // Generate a temporary token (in production, this should come from server)
        token = 'temp_' + Math.random().toString(36).substr(2, 9);
    }
    return token;
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, duration);
}

/**
 * Handle AJAX form submissions
 */
async function handleAjaxForm(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;

    // Disable submit button
    submitButton.disabled = true;
    submitButton.textContent = 'Processing...';

    try {
        const response = await fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message || 'Operation successful', 'success');

            // Handle specific success actions
            if (result.action === 'login') {
                isLoggedIn = true;
                closeModal(form.closest('.modal'));
                location.reload();
            } else if (result.action === 'register') {
                showNotification('Please check your email to verify your account', 'info');
                closeModal(form.closest('.modal'));
            }
        } else {
            showNotification(result.message || 'An error occurred', 'error');
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.textContent = originalText;
    }
}

/**
 * Check authentication status
 */
async function checkAuthStatus() {
    try {
        const response = await fetch('/api/auth-status.php');
        const result = await response.json();
        isLoggedIn = result.authenticated;

        if (isLoggedIn) {
            // Load cart from server
            loadCartFromServer();
        }
    } catch (error) {
        console.error('Auth status check error:', error);
    }
}

/**
 * Load cart from server
 */
async function loadCartFromServer() {
    try {
        const response = await fetch('/api/cart.php');
        const result = await response.json();

        if (result.success) {
            cart = result.cart || [];
            localStorage.setItem('omnistore-cart', JSON.stringify(cart));
            updateCartCount();
        }
    } catch (error) {
        console.error('Cart load error:', error);
    }
}
