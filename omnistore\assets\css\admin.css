/**
 * Admin Panel Styles
 * 
 * Specific styles for the admin dashboard and management interfaces
 */

/* Admin Layout */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-sidebar {
  width: 250px;
  background: var(--surface);
  border-right: 1px solid var(--border-primary);
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  overflow-y: auto;
  z-index: 40;
  transition: transform var(--transition-fast);
}

.admin-sidebar.collapsed {
  transform: translateX(-100%);
}

.admin-content {
  flex: 1;
  margin-left: 250px;
  padding-top: 64px;
  transition: margin-left var(--transition-fast);
}

.admin-content.expanded {
  margin-left: 0;
}

/* Admin Navigation */
.admin-nav {
  padding: var(--spacing-md);
}

.admin-nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.admin-nav-item:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.admin-nav-item.active {
  background: var(--primary-100);
  color: var(--primary-600);
}

.admin-nav-item svg {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
}

/* Data Tables */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th {
  background: var(--surface-secondary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-primary);
}

.data-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-secondary);
}

.data-table tr:hover {
  background: var(--surface-hover);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: var(--success-100);
  color: var(--success-800);
}

.status-badge.inactive {
  background: var(--error-100);
  color: var(--error-800);
}

.status-badge.pending {
  background: var(--warning-100);
  color: var(--warning-800);
}

.status-badge.draft {
  background: var(--secondary-100);
  color: var(--secondary-800);
}

/* Form Enhancements */
.form-section {
  background: var(--surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-primary);
}

.form-grid {
  display: grid;
  gap: var(--spacing-md);
}

.form-grid.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.form-grid.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
  .form-grid.cols-2,
  .form-grid.cols-3 {
    grid-template-columns: 1fr;
  }
}

/* Image Upload */
.image-upload {
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.image-upload:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
}

.image-upload.dragover {
  border-color: var(--primary-600);
  background: var(--primary-100);
}

.image-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.image-preview-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--surface-secondary);
}

.image-preview-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.image-preview-item .remove-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--error-600);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.action-btn.edit {
  background: var(--primary-100);
  color: var(--primary-600);
}

.action-btn.edit:hover {
  background: var(--primary-200);
}

.action-btn.delete {
  background: var(--error-100);
  color: var(--error-600);
}

.action-btn.delete:hover {
  background: var(--error-200);
}

.action-btn.view {
  background: var(--secondary-100);
  color: var(--secondary-600);
}

.action-btn.view:hover {
  background: var(--secondary-200);
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--surface);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.pagination-btn:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.pagination-btn.active {
  background: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Search and Filters */
.search-filters {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 250px;
}

.filter-select {
  min-width: 150px;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-sm);
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-change.positive {
  color: var(--success-600);
}

.stat-change.negative {
  color: var(--error-600);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-content {
    margin-left: 0;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input,
  .filter-select {
    min-width: auto;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .data-table {
    font-size: 0.875rem;
  }
  
  .data-table th,
  .data-table td {
    padding: var(--spacing-sm);
  }
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--primary-200);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-state svg {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  margin-bottom: var(--spacing-md);
}
