<?php
/**
 * Setup Email Templates
 * 
 * This script creates the default email templates in the database.
 * Run this once after setting up the database.
 */

require_once 'config/config.php';

try {
    echo "Setting up email templates...\n";
    
    $emailService = new EmailService();
    $emailService->createDefaultTemplates();
    
    echo "Email templates created successfully!\n";
    echo "You can now use the authentication system.\n";
    
} catch (Exception $e) {
    echo "Error setting up email templates: " . $e->getMessage() . "\n";
}
?>
