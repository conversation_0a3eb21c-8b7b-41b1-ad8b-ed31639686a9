<?php
/**
 * Admin Dashboard
 * 
 * Main admin panel for managing the e-commerce platform
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    redirect(BASE_URL . '/?login=required');
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    redirect(BASE_URL . '/?access=denied');
}

// Get dashboard statistics
try {
    $db = getDB();
    
    // Get counts
    $stats = [
        'total_products' => $db->fetchOne("SELECT COUNT(*) as count FROM products")['count'],
        'active_products' => $db->fetchOne("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'],
        'total_categories' => $db->fetchOne("SELECT COUNT(*) as count FROM categories")['count'],
        'total_users' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'customer'")['count'],
        'total_orders' => $db->fetchOne("SELECT COUNT(*) as count FROM orders")['count'] ?? 0,
        'pending_orders' => $db->fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'] ?? 0
    ];
    
    // Get recent products
    $recentProducts = $db->fetchAll(
        "SELECT p.*, c.name as category_name 
         FROM products p 
         LEFT JOIN categories c ON p.category_id = c.id 
         ORDER BY p.created_at DESC 
         LIMIT 5"
    );
    
    // Get low stock products
    $lowStockProducts = $db->fetchAll(
        "SELECT * FROM products 
         WHERE track_inventory = 1 AND stock_quantity <= low_stock_threshold AND is_active = 1
         ORDER BY stock_quantity ASC 
         LIMIT 5"
    );
    
} catch (Exception $e) {
    logError("Admin dashboard error: " . $e->getMessage());
    $stats = [];
    $recentProducts = [];
    $lowStockProducts = [];
}

$pageTitle = "Admin Dashboard - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/admin.css" rel="stylesheet">
</head>
<body class="bg-primary text-primary">
    <!-- Admin Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-8">
                    <a href="<?php echo BASE_URL; ?>" class="logo">
                        <?php echo SITE_NAME; ?> Admin
                    </a>
                    
                    <!-- Admin Menu -->
                    <div class="hidden md:flex space-x-6">
                        <a href="<?php echo BASE_URL; ?>/admin/" class="text-primary-600 font-medium">Dashboard</a>
                        <a href="<?php echo BASE_URL; ?>/admin/products.php" class="text-secondary hover:text-primary transition-colors">Products</a>
                        <a href="<?php echo BASE_URL; ?>/admin/categories.php" class="text-secondary hover:text-primary transition-colors">Categories</a>
                        <a href="<?php echo BASE_URL; ?>/admin/orders.php" class="text-secondary hover:text-primary transition-colors">Orders</a>
                        <a href="<?php echo BASE_URL; ?>/admin/users.php" class="text-secondary hover:text-primary transition-colors">Users</a>
                        <a href="<?php echo BASE_URL; ?>/admin/settings.php" class="text-secondary hover:text-primary transition-colors">Settings</a>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- View Site -->
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-outline btn-sm" target="_blank">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Site
                    </a>
                    
                    <!-- User Menu -->
                    <div class="user-dropdown">
                        <button class="flex items-center space-x-2 text-secondary hover:text-primary transition-colors">
                            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                <?php echo strtoupper(substr($currentUser['first_name'], 0, 1)); ?>
                            </div>
                            <span class="hidden md:inline text-sm font-medium">
                                <?php echo htmlspecialchars($currentUser['first_name']); ?>
                            </span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="px-4 py-3 border-b border-primary">
                                <p class="text-sm font-semibold text-primary">
                                    <?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?>
                                </p>
                                <p class="text-xs text-secondary">Administrator</p>
                            </div>
                            <div class="py-2">
                                <a href="<?php echo BASE_URL; ?>/admin/profile.php" class="flex items-center px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                    <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Profile
                                </a>
                            </div>
                            <div class="border-t border-primary pt-2">
                                <button onclick="logoutUser()" class="flex items-center w-full px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                    <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen">
        <div class="container mx-auto px-4 py-8">
            <!-- Page Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-primary mb-2">Dashboard</h1>
                <p class="text-secondary">Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>! Here's what's happening with your store.</p>
            </div>
            
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Total Products -->
                <div class="card p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-secondary mb-1">Total Products</p>
                            <p class="text-2xl font-bold text-primary"><?php echo number_format($stats['total_products'] ?? 0); ?></p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-sm text-success-600"><?php echo $stats['active_products'] ?? 0; ?> active</span>
                    </div>
                </div>
                
                <!-- Total Categories -->
                <div class="card p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-secondary mb-1">Categories</p>
                            <p class="text-2xl font-bold text-primary"><?php echo number_format($stats['total_categories'] ?? 0); ?></p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Total Users -->
                <div class="card p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-secondary mb-1">Customers</p>
                            <p class="text-2xl font-bold text-primary"><?php echo number_format($stats['total_users'] ?? 0); ?></p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Recent Products -->
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-primary">Recent Products</h2>
                        <a href="<?php echo BASE_URL; ?>/admin/products.php" class="text-primary-600 hover:text-primary-700 text-sm">View All</a>
                    </div>
                    
                    <?php if (empty($recentProducts)): ?>
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <p class="text-secondary">No products yet</p>
                            <a href="<?php echo BASE_URL; ?>/admin/products.php?action=create" class="btn btn-primary btn-sm mt-4">Add First Product</a>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($recentProducts as $product): ?>
                                <div class="flex items-center justify-between p-3 bg-secondary rounded-lg">
                                    <div>
                                        <p class="font-medium text-primary"><?php echo htmlspecialchars($product['name']); ?></p>
                                        <p class="text-sm text-secondary"><?php echo htmlspecialchars($product['category_name'] ?? 'No Category'); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-primary">$<?php echo number_format($product['price'], 2); ?></p>
                                        <span class="text-xs px-2 py-1 rounded-full <?php echo $product['is_active'] ? 'bg-success-100 text-success-800' : 'bg-error-100 text-error-800'; ?>">
                                            <?php echo $product['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Low Stock Alert -->
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-primary">Low Stock Alert</h2>
                        <span class="text-xs px-2 py-1 bg-warning-100 text-warning-800 rounded-full">
                            <?php echo count($lowStockProducts); ?> items
                        </span>
                    </div>
                    
                    <?php if (empty($lowStockProducts)): ?>
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-success-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-secondary">All products are well stocked!</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($lowStockProducts as $product): ?>
                                <div class="flex items-center justify-between p-3 bg-warning-50 border border-warning-200 rounded-lg">
                                    <div>
                                        <p class="font-medium text-primary"><?php echo htmlspecialchars($product['name']); ?></p>
                                        <p class="text-sm text-secondary">SKU: <?php echo htmlspecialchars($product['sku']); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-warning-800"><?php echo $product['stock_quantity']; ?> left</p>
                                        <p class="text-xs text-secondary">Threshold: <?php echo $product['low_stock_threshold']; ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card p-6">
                <h2 class="text-lg font-semibold text-primary mb-4">Quick Actions</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="<?php echo BASE_URL; ?>/admin/products.php?action=create" class="btn btn-primary">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Product
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/categories.php?action=create" class="btn btn-secondary">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        Add Category
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/products.php" class="btn btn-outline">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Manage Products
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/settings.php" class="btn btn-outline">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Settings
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/admin.js"></script>
</body>
</html>
