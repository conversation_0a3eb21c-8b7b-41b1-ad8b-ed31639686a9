/**
 * OmniStore Dynamic Theming System
 * 
 * This file contains CSS custom properties (variables) for implementing
 * a dynamic theming system with light/dark modes and smooth transitions.
 */

/* Root variables for light theme (default) */
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Semantic Colors - Light Theme */
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --surface: #ffffff;
  --surface-hover: #f8fafc;
  --surface-active: #f1f5f9;
  
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: var(--primary-500);
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Component Specific */
  --navbar-bg: rgba(255, 255, 255, 0.95);
  --navbar-border: var(--border-primary);
  --card-bg: var(--surface);
  --card-border: var(--border-primary);
  --input-bg: var(--surface);
  --input-border: var(--border-primary);
  --button-primary-bg: var(--primary-600);
  --button-primary-hover: var(--primary-700);
  --button-secondary-bg: var(--secondary-100);
  --button-secondary-hover: var(--secondary-200);

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
}

/* Dark theme variables */
[data-theme="dark"] {
  /* Semantic Colors - Dark Theme */
  --background: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --surface: #1e293b;
  --surface-hover: #334155;
  --surface-active: #475569;
  
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-inverse: #0f172a;
  
  --border-primary: #334155;
  --border-secondary: #475569;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);

  /* Component Specific - Dark Theme */
  --navbar-bg: rgba(30, 41, 59, 0.95);
  --navbar-border: var(--border-primary);
  --card-bg: var(--surface);
  --card-border: var(--border-primary);
  --input-bg: var(--surface);
  --input-border: var(--border-primary);
  --button-secondary-bg: var(--secondary-700);
  --button-secondary-hover: var(--secondary-600);
}

/* Auto theme detection based on system preference */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Apply dark theme variables when system prefers dark and no explicit theme is set */
    --background: #0f172a;
    --background-secondary: #1e293b;
    --background-tertiary: #334155;
    --surface: #1e293b;
    --surface-hover: #334155;
    --surface-active: #475569;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #0f172a;
    
    --border-primary: #334155;
    --border-secondary: #475569;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);

    --navbar-bg: rgba(30, 41, 59, 0.95);
    --navbar-border: var(--border-primary);
    --card-bg: var(--surface);
    --card-border: var(--border-primary);
    --input-bg: var(--surface);
    --input-border: var(--border-primary);
    --button-secondary-bg: var(--secondary-700);
    --button-secondary-hover: var(--secondary-600);
  }
}

/* Base styles with theme variables */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Theme transition for all elements */
*,
*::before,
*::after {
  transition: 
    background-color var(--transition-normal),
    border-color var(--transition-normal),
    color var(--transition-normal),
    box-shadow var(--transition-normal);
}

/* Utility classes for theme-aware components */
.bg-primary { background-color: var(--background); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-surface { background-color: var(--surface); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }

.border-primary { border-color: var(--border-primary); }
.border-secondary { border-color: var(--border-secondary); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
