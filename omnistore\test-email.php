<?php
/**
 * Email Test Page
 * 
 * Simple page to test email functionality
 * Remove this file in production
 */

require_once 'config/config.php';

$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $testEmail = Security::sanitizeInput($_POST['email'] ?? '');
    
    if (empty($testEmail) || !Security::validateEmail($testEmail)) {
        $message = 'Please enter a valid email address';
        $messageType = 'error';
    } else {
        try {
            $emailService = new EmailService();
            
            $subject = 'OmniStore Email Test';
            $body = '
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2563eb;">Email Test Successful!</h2>
                    <p>Hello,</p>
                    <p>This is a test email from your OmniStore e-commerce platform.</p>
                    <p>If you received this email, your email configuration is working correctly!</p>
                    <p><strong>Email Configuration:</strong></p>
                    <ul>
                        <li>SMTP Host: ' . SMTP_HOST . '</li>
                        <li>SMTP Port: ' . SMTP_PORT . '</li>
                        <li>From Email: ' . FROM_EMAIL . '</li>
                        <li>Encryption: ' . SMTP_ENCRYPTION . '</li>
                    </ul>
                    <p>Best regards,<br>The OmniStore Team</p>
                </div>
            </body>
            </html>';
            
            $success = $emailService->sendEmail($testEmail, $subject, $body, true);
            
            if ($success) {
                $message = 'Test email sent successfully to ' . htmlspecialchars($testEmail) . '! Please check your inbox.';
                $messageType = 'success';
            } else {
                $message = 'Failed to send test email. Please check your email configuration.';
                $messageType = 'error';
            }
            
        } catch (Exception $e) {
            $message = 'Email test failed: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

$pageTitle = "Email Test - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="text-2xl font-bold text-primary-600">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>
                <div class="text-sm text-secondary">
                    Email Configuration Test
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-2xl mx-auto">
                <div class="bg-surface rounded-lg shadow-lg p-8 border border-primary">
                    <h1 class="text-3xl font-bold text-primary mb-6">Email Configuration Test</h1>
                    
                    <!-- Current Configuration -->
                    <div class="mb-8 p-4 bg-secondary rounded-lg">
                        <h2 class="text-lg font-semibold text-primary mb-4">Current Email Settings</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <strong>SMTP Host:</strong> <?php echo htmlspecialchars(SMTP_HOST); ?>
                            </div>
                            <div>
                                <strong>SMTP Port:</strong> <?php echo htmlspecialchars(SMTP_PORT); ?>
                            </div>
                            <div>
                                <strong>Username:</strong> <?php echo htmlspecialchars(SMTP_USERNAME); ?>
                            </div>
                            <div>
                                <strong>Encryption:</strong> <?php echo htmlspecialchars(SMTP_ENCRYPTION); ?>
                            </div>
                            <div>
                                <strong>From Email:</strong> <?php echo htmlspecialchars(FROM_EMAIL); ?>
                            </div>
                            <div>
                                <strong>From Name:</strong> <?php echo htmlspecialchars(FROM_NAME); ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Message Display -->
                    <?php if (!empty($message)): ?>
                        <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-success-100 text-success-800 border border-success-200' : ($messageType === 'error' ? 'bg-error-100 text-error-800 border border-error-200' : 'bg-primary-100 text-primary-800 border border-primary-200'); ?>">
                            <div class="flex items-center">
                                <?php if ($messageType === 'success'): ?>
                                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                <?php elseif ($messageType === 'error'): ?>
                                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Test Form -->
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-group">
                            <label for="email" class="block text-sm font-medium text-primary mb-2">
                                Test Email Address
                            </label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   required 
                                   class="form-input w-full"
                                   placeholder="Enter email address to test"
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                            <p class="text-sm text-secondary mt-1">
                                Enter an email address where you want to receive the test email
                            </p>
                        </div>
                        
                        <div class="flex space-x-4">
                            <button type="submit" 
                                    name="test_email" 
                                    value="1" 
                                    class="btn btn-primary">
                                Send Test Email
                            </button>
                            
                            <a href="<?php echo BASE_URL; ?>" class="btn btn-secondary">
                                Back to Homepage
                            </a>
                        </div>
                    </form>
                    
                    <!-- Instructions -->
                    <div class="mt-8 p-4 bg-tertiary rounded-lg">
                        <h3 class="text-lg font-semibold text-primary mb-2">Instructions</h3>
                        <ol class="list-decimal list-inside text-sm text-secondary space-y-1">
                            <li>Enter your email address in the form above</li>
                            <li>Click "Send Test Email" to test the email configuration</li>
                            <li>Check your inbox (and spam folder) for the test email</li>
                            <li>If successful, your email system is working correctly</li>
                            <li>If failed, check your SMTP settings in config/config.php</li>
                        </ol>
                    </div>
                    
                    <!-- Warning -->
                    <div class="mt-6 p-4 bg-warning-100 border border-warning-200 rounded-lg">
                        <div class="flex items-center text-warning-800">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-sm">
                                <strong>Security Note:</strong> Remove this test file (test-email.php) in production environment.
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
