<?php
/**
 * Authentication Status API Endpoint
 * 
 * Returns current user authentication status and basic user info
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $auth = new Auth();
    $isAuthenticated = $auth->isLoggedIn();
    
    $response = [
        'authenticated' => $isAuthenticated,
        'user' => null
    ];
    
    if ($isAuthenticated) {
        $user = $auth->getCurrentUser();
        if ($user) {
            $response['user'] = [
                'id' => $user['id'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'email' => $user['email'],
                'theme_preference' => $user['theme_preference'],
                'profile_image' => $user['profile_image']
            ];
        }
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    logError("Auth status API error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(['authenticated' => false, 'user' => null]);
}
?>
