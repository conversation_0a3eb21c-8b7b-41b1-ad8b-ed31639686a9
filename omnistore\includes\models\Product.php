<?php
/**
 * Product Model
 * 
 * Handles all product-related database operations and business logic
 */

class Product {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Create a new product
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['name', 'description', 'price', 'category_id'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new Exception("Field '$field' is required");
                }
            }
            
            // Generate unique SKU if not provided
            if (empty($data['sku'])) {
                $data['sku'] = $this->generateSKU($data['category_id']);
            }
            
            // Sanitize and validate data
            $productData = [
                'id' => generateUniqueId('PRD', 16),
                'name' => Security::sanitizeInput($data['name']),
                'description' => Security::sanitizeInput($data['description']),
                'short_description' => Security::sanitizeInput($data['short_description'] ?? ''),
                'sku' => Security::sanitizeInput($data['sku']),
                'price' => floatval($data['price']),
                'compare_price' => !empty($data['compare_price']) ? floatval($data['compare_price']) : null,
                'cost_price' => !empty($data['cost_price']) ? floatval($data['cost_price']) : null,
                'category_id' => intval($data['category_id']),
                'brand' => Security::sanitizeInput($data['brand'] ?? ''),
                'weight' => !empty($data['weight']) ? floatval($data['weight']) : null,
                'dimensions' => Security::sanitizeInput($data['dimensions'] ?? ''),
                'stock_quantity' => intval($data['stock_quantity'] ?? 0),
                'low_stock_threshold' => intval($data['low_stock_threshold'] ?? 5),
                'track_inventory' => !empty($data['track_inventory']) ? 1 : 0,
                'is_featured' => !empty($data['is_featured']) ? 1 : 0,
                'is_active' => !empty($data['is_active']) ? 1 : 0,
                'meta_title' => Security::sanitizeInput($data['meta_title'] ?? ''),
                'meta_description' => Security::sanitizeInput($data['meta_description'] ?? ''),
                'tags' => Security::sanitizeInput($data['tags'] ?? ''),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Validate price
            if ($productData['price'] <= 0) {
                throw new Exception("Price must be greater than 0");
            }
            
            // Check if SKU already exists
            if ($this->skuExists($productData['sku'])) {
                throw new Exception("SKU already exists");
            }
            
            // Insert product
            $sql = "INSERT INTO products (" . implode(', ', array_keys($productData)) . ") 
                    VALUES (:" . implode(', :', array_keys($productData)) . ")";
            
            $this->db->execute($sql, $productData);
            
            // Log product creation
            logError("Product created: " . $productData['name'] . " (ID: " . $productData['id'] . ")");
            
            return [
                'success' => true,
                'message' => 'Product created successfully',
                'product_id' => $productData['id']
            ];
            
        } catch (Exception $e) {
            logError("Product creation error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get product by ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug 
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE p.id = :id";
            
            $product = $this->db->fetchOne($sql, ['id' => $id]);
            
            if (!$product) {
                return null;
            }
            
            // Get product images
            $product['images'] = $this->getProductImages($id);
            
            // Get product attributes
            $product['attributes'] = $this->getProductAttributes($id);
            
            return $product;
            
        } catch (Exception $e) {
            logError("Get product error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Update product
     */
    public function update($id, $data) {
        try {
            // Check if product exists
            $existing = $this->getById($id);
            if (!$existing) {
                throw new Exception("Product not found");
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = [
                'name', 'description', 'short_description', 'sku', 'price', 
                'compare_price', 'cost_price', 'category_id', 'brand', 'weight', 
                'dimensions', 'stock_quantity', 'low_stock_threshold', 
                'track_inventory', 'is_featured', 'is_active', 'meta_title', 
                'meta_description', 'tags'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    switch ($field) {
                        case 'price':
                        case 'compare_price':
                        case 'cost_price':
                        case 'weight':
                            $updateData[$field] = !empty($data[$field]) ? floatval($data[$field]) : null;
                            break;
                        case 'category_id':
                        case 'stock_quantity':
                        case 'low_stock_threshold':
                            $updateData[$field] = intval($data[$field]);
                            break;
                        case 'track_inventory':
                        case 'is_featured':
                        case 'is_active':
                            $updateData[$field] = !empty($data[$field]) ? 1 : 0;
                            break;
                        default:
                            $updateData[$field] = Security::sanitizeInput($data[$field]);
                    }
                }
            }
            
            // Validate price if being updated
            if (isset($updateData['price']) && $updateData['price'] <= 0) {
                throw new Exception("Price must be greater than 0");
            }
            
            // Check SKU uniqueness if being updated
            if (isset($updateData['sku']) && $updateData['sku'] !== $existing['sku']) {
                if ($this->skuExists($updateData['sku'])) {
                    throw new Exception("SKU already exists");
                }
            }
            
            if (empty($updateData)) {
                return [
                    'success' => true,
                    'message' => 'No changes to update'
                ];
            }
            
            // Add updated timestamp
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            // Build update query
            $setParts = [];
            foreach ($updateData as $field => $value) {
                $setParts[] = "$field = :$field";
            }
            
            $sql = "UPDATE products SET " . implode(', ', $setParts) . " WHERE id = :id";
            $updateData['id'] = $id;
            
            $this->db->execute($sql, $updateData);
            
            // Log product update
            logError("Product updated: " . ($updateData['name'] ?? $existing['name']) . " (ID: $id)");
            
            return [
                'success' => true,
                'message' => 'Product updated successfully'
            ];
            
        } catch (Exception $e) {
            logError("Product update error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete product
     */
    public function delete($id) {
        try {
            // Check if product exists
            $product = $this->getById($id);
            if (!$product) {
                throw new Exception("Product not found");
            }
            
            // Delete product images first
            $this->deleteProductImages($id);
            
            // Delete product attributes
            $this->deleteProductAttributes($id);
            
            // Delete the product
            $sql = "DELETE FROM products WHERE id = :id";
            $this->db->execute($sql, ['id' => $id]);
            
            // Log product deletion
            logError("Product deleted: " . $product['name'] . " (ID: $id)");
            
            return [
                'success' => true,
                'message' => 'Product deleted successfully'
            ];
            
        } catch (Exception $e) {
            logError("Product deletion error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get all products with pagination and filtering
     */
    public function getAll($options = []) {
        try {
            $page = intval($options['page'] ?? 1);
            $limit = intval($options['limit'] ?? 20);
            $offset = ($page - 1) * $limit;
            
            $where = ['1=1'];
            $params = [];
            
            // Category filter
            if (!empty($options['category_id'])) {
                $where[] = 'p.category_id = :category_id';
                $params['category_id'] = $options['category_id'];
            }
            
            // Search filter
            if (!empty($options['search'])) {
                $where[] = '(p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)';
                $params['search'] = '%' . $options['search'] . '%';
            }
            
            // Status filter
            if (isset($options['is_active'])) {
                $where[] = 'p.is_active = :is_active';
                $params['is_active'] = $options['is_active'] ? 1 : 0;
            }
            
            // Featured filter
            if (isset($options['is_featured'])) {
                $where[] = 'p.is_featured = :is_featured';
                $params['is_featured'] = $options['is_featured'] ? 1 : 0;
            }
            
            // Price range filter
            if (!empty($options['min_price'])) {
                $where[] = 'p.price >= :min_price';
                $params['min_price'] = floatval($options['min_price']);
            }
            
            if (!empty($options['max_price'])) {
                $where[] = 'p.price <= :max_price';
                $params['max_price'] = floatval($options['max_price']);
            }
            
            // Build query
            $whereClause = implode(' AND ', $where);
            $orderBy = $options['order_by'] ?? 'p.created_at DESC';
            
            // Get total count
            $countSql = "SELECT COUNT(*) as total FROM products p 
                         LEFT JOIN categories c ON p.category_id = c.id 
                         WHERE $whereClause";
            $totalResult = $this->db->fetchOne($countSql, $params);
            $total = $totalResult['total'];
            
            // Get products
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug,
                           (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    WHERE $whereClause 
                    ORDER BY $orderBy 
                    LIMIT :limit OFFSET :offset";
            
            $params['limit'] = $limit;
            $params['offset'] = $offset;
            
            $products = $this->db->fetchAll($sql, $params);
            
            return [
                'success' => true,
                'products' => $products,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];
            
        } catch (Exception $e) {
            logError("Get products error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'products' => [],
                'pagination' => ['page' => 1, 'limit' => $limit, 'total' => 0, 'pages' => 0]
            ];
        }
    }
    
    /**
     * Generate unique SKU
     */
    private function generateSKU($categoryId) {
        $category = $this->db->fetchOne("SELECT slug FROM categories WHERE id = :id", ['id' => $categoryId]);
        $prefix = $category ? strtoupper(substr($category['slug'], 0, 3)) : 'PRD';
        
        do {
            $sku = generateProductSKU($prefix);
        } while ($this->skuExists($sku));
        
        return $sku;
    }
    
    /**
     * Check if SKU exists
     */
    private function skuExists($sku) {
        $result = $this->db->fetchOne("SELECT id FROM products WHERE sku = :sku", ['sku' => $sku]);
        return !empty($result);
    }
    
    /**
     * Get product images
     */
    private function getProductImages($productId) {
        return $this->db->fetchAll(
            "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY is_primary DESC, sort_order ASC",
            ['product_id' => $productId]
        );
    }
    
    /**
     * Get product attributes
     */
    private function getProductAttributes($productId) {
        return $this->db->fetchAll(
            "SELECT * FROM product_attributes WHERE product_id = :product_id ORDER BY sort_order ASC",
            ['product_id' => $productId]
        );
    }
    
    /**
     * Delete product images
     */
    private function deleteProductImages($productId) {
        // Get image files to delete from filesystem
        $images = $this->getProductImages($productId);
        foreach ($images as $image) {
            $imagePath = ROOT_PATH . '/uploads/products/' . basename($image['image_url']);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
        
        // Delete from database
        $this->db->execute("DELETE FROM product_images WHERE product_id = :product_id", ['product_id' => $productId]);
    }
    
    /**
     * Delete product attributes
     */
    private function deleteProductAttributes($productId) {
        $this->db->execute("DELETE FROM product_attributes WHERE product_id = :product_id", ['product_id' => $productId]);
    }
}
?>
