<?php
/**
 * OmniStore - Main Entry Point
 *
 * This is the main homepage for the OmniStore e-commerce platform.
 * It displays featured products, categories, and promotional content.
 */

require_once 'config/config.php';

// Check authentication status
$auth = new Auth();
$isUserLoggedIn = $auth->isLoggedIn();
$currentUser = null;

if ($isUserLoggedIn) {
    $currentUser = $auth->getCurrentUser();
}

// Get featured products and categories for homepage
try {
    $db = getDB();

    // Get featured products
    $featuredProducts = $db->fetchAll(
        "SELECT p.*, pi.image_url, c.name as category_name
         FROM products p
         LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
         LEFT JOIN categories c ON p.category_id = c.id
         WHERE p.is_featured = 1 AND p.is_active = 1
         ORDER BY p.created_at DESC
         LIMIT 8"
    );

    // Get active categories
    $categories = $db->fetchAll(
        "SELECT * FROM categories
         WHERE is_active = 1 AND parent_id IS NULL
         ORDER BY sort_order ASC, name ASC"
    );

} catch (Exception $e) {
    logError("Homepage data fetch error: " . $e->getMessage());
    $featuredProducts = [];
    $categories = [];
}

$pageTitle = "Welcome to " . SITE_NAME;
$pageDescription = SITE_DESCRIPTION;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars(SITE_KEYWORDS); ?>">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo ASSETS_URL; ?>/js/theme.js" as="script">
    <link rel="preload" href="<?php echo ASSETS_URL; ?>/js/main.js" as="script">
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="logo">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <div class="relative w-full">
                        <input type="text"
                               placeholder="Search products..."
                               class="w-full px-4 py-2 pl-10 pr-4 bg-surface border border-primary rounded-lg focus:outline-none focus:border-focus">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center space-x-4">
                    <!-- Categories Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-secondary hover:text-primary transition-colors">
                            <span>Categories</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-1 w-48 bg-surface border border-primary rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <?php foreach ($categories as $category): ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>"
                                   class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Cart -->
                    <a href="<?php echo BASE_URL; ?>/cart.php" class="relative text-secondary hover:text-primary transition-colors">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0h9"></path>
                        </svg>
                        <span class="absolute -top-2 -right-2 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                    </a>

                    <!-- User Account -->
                    <?php if ($isUserLoggedIn && $currentUser): ?>
                        <div class="user-dropdown">
                            <button class="flex items-center space-x-2 text-secondary hover:text-primary transition-colors">
                                <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1)); ?>
                                </div>
                                <span class="hidden md:inline text-sm font-medium">
                                    <?php echo htmlspecialchars($currentUser['first_name']); ?>
                                </span>
                                <svg class="h-4 w-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="dropdown-menu">
                                <div class="px-4 py-3 border-b border-primary">
                                    <p class="text-sm font-semibold text-primary">
                                        <?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?>
                                    </p>
                                    <p class="text-xs text-secondary">
                                        <?php echo htmlspecialchars($currentUser['email']); ?>
                                    </p>
                                </div>
                                <div class="py-2">
                                    <a href="<?php echo BASE_URL; ?>/account.php" class="flex items-center px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                        <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        My Account
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>/orders.php" class="flex items-center px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                        <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                        My Orders
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>/wishlist.php" class="flex items-center px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                        <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        Wishlist
                                    </a>
                                </div>
                                <div class="border-t border-primary pt-2">
                                    <button onclick="logoutUser()" class="flex items-center w-full px-4 py-2 text-sm text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                        <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <button onclick="openLoginModal()" class="flex items-center space-x-2 text-secondary hover:text-primary transition-colors" title="Login">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="hidden md:inline text-sm">Login</span>
                        </button>
                    <?php endif; ?>

                    <!-- Mobile Menu Toggle -->
                    <button class="md:hidden text-secondary hover:text-primary transition-colors" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        <!-- Enhanced Hero Section -->
        <section class="relative bg-gradient-to-br from-primary-50 via-primary-100 to-secondary-50 py-12 overflow-hidden">
            <!-- Background Elements -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-10 left-10 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
                <div class="absolute top-40 right-10 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style="animation-delay: 2s;"></div>
                <div class="absolute bottom-10 left-1/2 w-72 h-72 bg-accent-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style="animation-delay: 4s;"></div>
            </div>

            <div class="container mx-auto px-4 relative z-10">
                <div class="text-center max-w-5xl mx-auto">
                    <div class="mb-4">
                        <span class="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-2">
                            🎉 Welcome to the Future of Shopping
                        </span>
                    </div>

                    <h1 class="text-5xl md:text-6xl font-bold text-primary mb-4 leading-tight">
                        Welcome to
                        <span class="bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 bg-clip-text text-transparent">
                            <?php echo SITE_NAME; ?>
                        </span>
                    </h1>

                    <p class="text-lg md:text-xl text-secondary mb-8 max-w-3xl mx-auto leading-relaxed">
                        <?php echo SITE_DESCRIPTION; ?> - Discover amazing products at unbeatable prices with fast, secure delivery.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                        <a href="<?php echo BASE_URL; ?>/products.php" class="btn btn-primary btn-lg group">
                            <span>Shop Now</span>
                            <svg class="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                        <a href="#categories" class="btn btn-outline btn-lg group">
                            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            <span>Browse Categories</span>
                        </a>
                    </div>

                    <!-- Stats Section -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                        <div class="glass-card p-6 rounded-xl">
                            <div class="text-3xl font-bold text-primary-600 mb-2">10K+</div>
                            <div class="text-secondary">Happy Customers</div>
                        </div>
                        <div class="glass-card p-6 rounded-xl">
                            <div class="text-3xl font-bold text-primary-600 mb-2">50K+</div>
                            <div class="text-secondary">Products Available</div>
                        </div>
                        <div class="glass-card p-6 rounded-xl">
                            <div class="text-3xl font-bold text-primary-600 mb-2">24/7</div>
                            <div class="text-secondary">Customer Support</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section id="categories" class="py-16 bg-secondary">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">Shop by Category</h2>
                    <p class="text-lg text-secondary max-w-2xl mx-auto">
                        Explore our carefully curated categories to find exactly what you're looking for
                    </p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                    <!-- Electronics -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=electronics" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Electronics
                            </h3>
                        </div>
                    </a>

                    <!-- Fashion -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=fashion" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Fashion
                            </h3>
                        </div>
                    </a>

                    <!-- Home & Garden -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=home-garden" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Home & Garden
                            </h3>
                        </div>
                    </a>

                    <!-- Health & Beauty -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=health-beauty" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Health & Beauty
                            </h3>
                        </div>
                    </a>

                    <!-- Sports & Outdoors -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=sports-outdoors" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Sports & Outdoors
                            </h3>
                        </div>
                    </a>

                    <!-- Books & Media -->
                    <a href="<?php echo BASE_URL; ?>/products.php?category=books-media" class="group">
                        <div class="bg-surface rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary hover:border-primary-300">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors text-sm">
                                Books & Media
                            </h3>
                        </div>
                    </a>
                </div>

                <!-- View All Categories Button -->
                <div class="text-center mt-8">
                    <a href="<?php echo BASE_URL; ?>/categories.php" class="btn btn-outline">
                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        View All Categories
                    </a>
                </div>
            </div>
        </section>

        <!-- Featured Products Section -->
        <section class="py-16">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12 text-primary">Featured Products</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php foreach ($featuredProducts as $product): ?>
                        <div class="bg-surface rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-primary">
                            <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg">
                                <img src="<?php echo $product['image_url'] ? UPLOADS_URL . '/products/' . $product['image_url'] : ASSETS_URL . '/images/placeholder-product.jpg'; ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-primary mb-2 line-clamp-2">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </h3>
                                <p class="text-secondary text-sm mb-2">
                                    <?php echo htmlspecialchars($product['category_name']); ?>
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xl font-bold text-primary-600">
                                        <?php echo formatCurrency($product['price']); ?>
                                    </span>
                                    <button onclick="addToCart(<?php echo $product['id']; ?>)"
                                            class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($featuredProducts)): ?>
                    <div class="text-center py-12">
                        <p class="text-secondary text-lg">No featured products available at the moment.</p>
                        <a href="<?php echo BASE_URL; ?>/products.php" class="inline-block mt-4 text-primary-600 hover:text-primary-700 font-semibold">
                            Browse All Products →
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-secondary text-primary py-12 border-t border-primary">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary"><?php echo SITE_NAME; ?></h3>
                    <p class="text-secondary"><?php echo SITE_DESCRIPTION; ?></p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo BASE_URL; ?>/products.php" class="text-secondary hover:text-primary transition-colors">All Products</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/about.php" class="text-secondary hover:text-primary transition-colors">About Us</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/contact.php" class="text-secondary hover:text-primary transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Customer Service</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo BASE_URL; ?>/help.php" class="text-secondary hover:text-primary transition-colors">Help Center</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/shipping.php" class="text-secondary hover:text-primary transition-colors">Shipping Info</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/returns.php" class="text-secondary hover:text-primary transition-colors">Returns</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Facebook</a>
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Twitter</a>
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Instagram</a>
                    </div>
                </div>
            </div>
            <div class="border-t border-primary mt-8 pt-8 text-center">
                <p class="text-secondary">&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
