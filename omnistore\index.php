<?php
/**
 * OmniStore - Main Entry Point
 *
 * This is the main homepage for the OmniStore e-commerce platform.
 * It displays featured products, categories, and promotional content.
 */

require_once 'config/config.php';

// Get featured products and categories for homepage
try {
    $db = getDB();

    // Get featured products
    $featuredProducts = $db->fetchAll(
        "SELECT p.*, pi.image_url, c.name as category_name
         FROM products p
         LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
         LEFT JOIN categories c ON p.category_id = c.id
         WHERE p.is_featured = 1 AND p.is_active = 1
         ORDER BY p.created_at DESC
         LIMIT 8"
    );

    // Get active categories
    $categories = $db->fetchAll(
        "SELECT * FROM categories
         WHERE is_active = 1 AND parent_id IS NULL
         ORDER BY sort_order ASC, name ASC"
    );

} catch (Exception $e) {
    logError("Homepage data fetch error: " . $e->getMessage());
    $featuredProducts = [];
    $categories = [];
}

$pageTitle = "Welcome to " . SITE_NAME;
$pageDescription = SITE_DESCRIPTION;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars(SITE_KEYWORDS); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/theme.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo ASSETS_URL; ?>/js/theme.js" as="script">
    <link rel="preload" href="<?php echo ASSETS_URL; ?>/js/main.js" as="script">
</head>
<body class="bg-primary text-primary">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50 bg-surface border-b border-primary backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo BASE_URL; ?>" class="text-2xl font-bold text-primary-600">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <div class="relative w-full">
                        <input type="text"
                               placeholder="Search products..."
                               class="w-full px-4 py-2 pl-10 pr-4 bg-surface border border-primary rounded-lg focus:outline-none focus:border-focus">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center space-x-4">
                    <!-- Categories Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-secondary hover:text-primary transition-colors">
                            <span>Categories</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-1 w-48 bg-surface border border-primary rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <?php foreach ($categories as $category): ?>
                                <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>"
                                   class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Cart -->
                    <a href="<?php echo BASE_URL; ?>/cart.php" class="relative text-secondary hover:text-primary transition-colors">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0h9"></path>
                        </svg>
                        <span class="absolute -top-2 -right-2 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                    </a>

                    <!-- User Account -->
                    <?php if (isLoggedIn()): ?>
                        <div class="relative group">
                            <button class="flex items-center space-x-1 text-secondary hover:text-primary transition-colors">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </button>
                            <div class="absolute top-full right-0 mt-1 w-48 bg-surface border border-primary rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <a href="<?php echo BASE_URL; ?>/account.php" class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">My Account</a>
                                <a href="<?php echo BASE_URL; ?>/orders.php" class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">My Orders</a>
                                <a href="<?php echo BASE_URL; ?>/wishlist.php" class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">Wishlist</a>
                                <hr class="border-primary my-1">
                                <a href="<?php echo BASE_URL; ?>/logout.php" class="block px-4 py-2 text-secondary hover:text-primary hover:bg-surface-hover transition-colors">Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <button onclick="openLoginModal()" class="text-secondary hover:text-primary transition-colors">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </button>
                    <?php endif; ?>

                    <!-- Mobile Menu Toggle -->
                    <button class="md:hidden text-secondary hover:text-primary transition-colors" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
            <div class="container mx-auto px-4 text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">Welcome to <?php echo SITE_NAME; ?></h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90"><?php echo SITE_DESCRIPTION; ?></p>
                <a href="<?php echo BASE_URL; ?>/products.php"
                   class="inline-block bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Shop Now
                </a>
            </div>
        </section>

        <!-- Categories Section -->
        <section class="py-16 bg-secondary">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12 text-primary">Shop by Category</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <?php foreach ($categories as $category): ?>
                        <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo urlencode($category['slug']); ?>"
                           class="group bg-surface rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 border border-primary">
                            <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                                <span class="text-2xl">📦</span>
                            </div>
                            <h3 class="font-semibold text-primary group-hover:text-primary-600 transition-colors">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </h3>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Featured Products Section -->
        <section class="py-16">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12 text-primary">Featured Products</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php foreach ($featuredProducts as $product): ?>
                        <div class="bg-surface rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-primary">
                            <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg">
                                <img src="<?php echo $product['image_url'] ? UPLOADS_URL . '/products/' . $product['image_url'] : ASSETS_URL . '/images/placeholder-product.jpg'; ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-primary mb-2 line-clamp-2">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </h3>
                                <p class="text-secondary text-sm mb-2">
                                    <?php echo htmlspecialchars($product['category_name']); ?>
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xl font-bold text-primary-600">
                                        <?php echo formatCurrency($product['price']); ?>
                                    </span>
                                    <button onclick="addToCart(<?php echo $product['id']; ?>)"
                                            class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($featuredProducts)): ?>
                    <div class="text-center py-12">
                        <p class="text-secondary text-lg">No featured products available at the moment.</p>
                        <a href="<?php echo BASE_URL; ?>/products.php" class="inline-block mt-4 text-primary-600 hover:text-primary-700 font-semibold">
                            Browse All Products →
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-secondary text-primary py-12 border-t border-primary">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary"><?php echo SITE_NAME; ?></h3>
                    <p class="text-secondary"><?php echo SITE_DESCRIPTION; ?></p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo BASE_URL; ?>/products.php" class="text-secondary hover:text-primary transition-colors">All Products</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/about.php" class="text-secondary hover:text-primary transition-colors">About Us</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/contact.php" class="text-secondary hover:text-primary transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Customer Service</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo BASE_URL; ?>/help.php" class="text-secondary hover:text-primary transition-colors">Help Center</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/shipping.php" class="text-secondary hover:text-primary transition-colors">Shipping Info</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/returns.php" class="text-secondary hover:text-primary transition-colors">Returns</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 text-primary">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Facebook</a>
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Twitter</a>
                        <a href="#" class="text-secondary hover:text-primary transition-colors">Instagram</a>
                    </div>
                </div>
            </div>
            <div class="border-t border-primary mt-8 pt-8 text-center">
                <p class="text-secondary">&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/theme.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
